import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 开发环境代理前缀
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { code, message, data } = response.data
    
    if (code === 200) {
      return { success: true, data, message }
    } else {
      ElMessage.error(message || '请求失败')
      return { success: false, message }
    }
  },
  error => {
    let message = '网络请求失败'
    
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 401:
          message = '未授权，请重新登录'
          localStorage.removeItem('admin_token')
          window.location.href = '/login'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = error.response.data?.message || '请求失败'
      }
    }
    
    ElMessage.error(message)
    return Promise.reject({ success: false, message })
  }
)

// API接口定义
export const adminAPI = {
  // 认证相关
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh')
  },
  
  // 镜像端管理
  mirrors: {
    list: () => api.get('/admin/mirrors'),
    getById: (id) => api.get(`/admin/mirrors/${id}`),
    updateStatus: (id, status) => api.put(`/admin/mirrors/${id}/status`, { status }),
    setTimeLimit: (id, timeConfig) => api.put(`/admin/mirrors/${id}/time-limit`, timeConfig),
    getHeartbeat: (id) => api.get(`/admin/mirrors/${id}/heartbeat`)
  },
  
  // 配置管理
  config: {
    getGlobalConfig: () => api.get('/admin/config'),
    updateGlobalConfig: (config) => api.put('/admin/config', config)
  },
  
  // 数据监控
  monitoring: {
    getTransactionData: (mirrorId, dateRange) => api.get(`/monitoring/transactions/${mirrorId}`, { params: dateRange }),
    getSystemStats: () => api.get('/monitoring/stats'),
    getRealTimeData: () => api.get('/monitoring/realtime')
  },
  
  // 工单管理
  tickets: {
    list: (params) => api.get('/tickets', { params }),
    getById: (id) => api.get(`/tickets/${id}`),
    update: (id, data) => api.put(`/tickets/${id}`, data),
    reply: (id, reply) => api.post(`/tickets/${id}/reply`, reply)
  }
}

export default api 