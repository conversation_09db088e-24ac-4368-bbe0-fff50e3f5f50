[package]
name = "pump-parser"
version = "1.0.0"
edition = "2021"
authors = ["Arbitrage Team"]
description = "Solana PumpFun CPI事件和指令解析器 - 支持完整的交易数据解析"
license = "MIT"
repository = "https://github.com/arbitrage-team/pump-parser"
keywords = ["solana", "pumpfun", "defi", "parser", "cpi"]
categories = ["cryptography::cryptocurrencies", "parsing"]
readme = "README.md"

[dependencies]
# 核心序列化
borsh = "0.10"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_with = "3.4.0"

# Solana相关 - 与参考代码保持一致
solana-program = "2.1.7"
solana-sdk = "2.1.7"

# 数据处理
base64 = "0.21.0"
bs58 = "0.5.0"
hex = "0.4.3"

# 时间处理
chrono = { version = "0.4.31", features = ["serde"] }

# 数值计算
rust_decimal = { version = "1.32", features = ["serde-float"] }

# 错误处理
anyhow = "1.0.75"
thiserror = "1.0"

# 日志
log = "0.4.17"
env_logger = "0.10.0"

# 异步运行时
tokio = { version = "1.21.2", features = ["full"] }
tokio-stream = "0.1"

# gRPC客户端 - 与参考代码保持一致
yellowstone-grpc-client = "4.0.0"
yellowstone-grpc-proto = { version = "4.0.0", default-features = false, features = ["plugin"] }

# Redis客户端
redis = { version = "0.25.3", features = ["tokio-comp"] }

# 添加缺少的依赖
tonic = { version = "0.12.1" }
futures-util = "0.3.31"
futures = "0.3.24"
clap = { version = "4.4.8", features = ["derive"] }
async-trait = "0.1.77"
toml = "0.8.17"

# 终极流水线新增依赖
crossbeam-channel = "0.5"
crossbeam-utils = "0.8"
dashmap = "5.5"
parking_lot = "0.12"
rayon = "1.8"
num_cpus = "1.16"
compact_str = "0.7"
regex = "1.10"
object-pool = "0.5"
openssl-sys = "0.9.109"
once_cell = "1.19"

[lib]
name = "pump_parser"
path = "src/lib.rs"

[[bin]]
name = "pump-demo"
path = "src/main.rs"
