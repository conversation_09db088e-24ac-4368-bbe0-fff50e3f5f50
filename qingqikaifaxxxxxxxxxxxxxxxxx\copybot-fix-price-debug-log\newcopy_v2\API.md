# 动态筛选器配置API

本系统支持通过Redis的Pub/Sub机制，在程序不重启的情况下，对交易筛选器的配置进行热更新。

## 机制

-   **通信方式**: Redis Pub/Sub
-   **控制频道**: `filter_update_channel`

## 使用方法

您可以使用任何Redis客户端（例如 `redis-cli`）向指定的控制频道发布一条消息。消息的Payload必须是一个符合格式的JSON字符串。

### 使用 `redis-cli` 的示例

```bash
redis-cli PUBLISH filter_update_channel '{"monitored_wallets":["Your_Wallet_Address_1","Your_Wallet_Address_2"],"sol_buy_range":[1.5, 10.0]}'
```

## JSON 配置格式

Payload是一个JSON对象，其字段对应`FilterConfig`结构体。**所有字段都是可选的**，您只需提供您想要**更新**的字段即可。

### 字段说明

| 字段名              | 类型             | 说明                                                              |
| ------------------- | ---------------- | ----------------------------------------------------------------- |
| `monitored_wallets` | `[String]`       | 一个包含所有需要监控的钱包地址的数组。                              |
| `sol_buy_range`     | `[f64, f64]`     | 一个包含两个浮点数的数组，代表SOL购买金额的[最小值, 最大值]范围。 |
| `price_range`       | `[f64, f64]`     | 一个包含两个浮点数的数组，代表代币价格的[最小值, 最大值]范围。    |

### 示例

1.  **更新监控钱包列表，并设置SOL购买范围为 1.5 到 10.0 SOL**
    (价格范围保持不变)
    ```json
    {
      "monitored_wallets": ["Your_Wallet_Address_1", "Your_Wallet_Address_2"],
      "sol_buy_range": [1.5, 10.0]
    }
    ```

2.  **只更新价格范围为 0.0001 到 0.0005**
    (钱包列表和SOL购买范围保持不变)
    ```json
    {
      "price_range": [0.0001, 0.0005]
    }
    ```

3.  **清空所有筛选条件，相当于监控所有交易**
    ```json
    {
        "monitored_wallets": [],
        "sol_buy_range": null,
        "price_range": null
    }
    ```
    *注意：将范围设置为`null`会移除该范围限制。* 