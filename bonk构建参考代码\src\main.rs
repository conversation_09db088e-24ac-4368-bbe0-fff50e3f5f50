use anyhow::Result;
use redis::{Client, Commands, PubSub, AsyncCommands};
use serde::{Deserialize, Serialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::Instruction,
    message::Message,
    pubkey::Pubkey,
    signature::{Keypair, Signature},
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use std::io::{self, Write};
use std::str::FromStr;
use std::time::Duration;
use std::collections::HashMap;
use futures_util::StreamExt;

mod raydium;
use raydium::{
    create_swap_instruction, 
    get_associated_token_address,
    create_associated_token_account_instruction,
    find_pool_authority,
    find_serum_vault_signer,
    WSOL_MINT,
};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct TradingData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

pub struct TradingBot {
    redis_client: Client,
    rpc_client: RpcClient,
    keypair: Keypair,
    priority_fee: u64,
    compute_limit: u32,
    wsol_amount: u64,
    target_tokens: HashMap<String, bool>, // token地址 -> 是否启用交易
}

impl TradingBot {
    pub fn new(
        redis_url: &str,
        rpc_url: &str,
        private_key_base58: &str,
        priority_fee: u64,
        compute_limit: u32,
    ) -> Result<Self> {
        let redis_client = Client::open(redis_url)?;
        let rpc_client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());
        
        // 解析Base58私钥
        let decoded = bs58::decode(private_key_base58).into_vec()?;
        let keypair = Keypair::from_bytes(&decoded)?;
        
        let wsol_amount = 1_000_000; // 固定0.001 SOL

        Ok(Self {
            redis_client,
            rpc_client,
            keypair,
            priority_fee,
            compute_limit,
            wsol_amount,
            target_tokens: HashMap::new(),
        })
    }

    pub async fn manual_buy(&self, token_address: &str) -> Result<()> {
        println!("🔍 订阅Redis频道等待Token: {} 的交易数据...", token_address);
        
        // 创建新的Redis连接
        let client = redis::Client::open(self.redis_client.get_connection_info().clone())?;
        let mut con = client.get_async_connection().await?;
        let mut pubsub = con.into_pubsub();
        
        // 订阅所有交易事件频道
        pubsub.subscribe("trade_events:all").await?;
        println!("✅ 已订阅Redis频道: trade_events:all");
        println!("📡 等待Token {} 的交易数据...", token_address);
        
        // 监听消息
        let mut stream = pubsub.on_message();
        
        loop {
            if let Some(msg) = stream.next().await {
                let payload: String = msg.get_payload()?;
                
                match serde_json::from_str::<TradingData>(&payload) {
                    Ok(trading_data) => {
                        // 跳过mint_address为"unknown"的消息
                        if trading_data.mint_address == "unknown" {
                            println!("⏭️ 跳过未知Token地址的消息");
                            continue;
                        }
                        
                        if trading_data.mint_address == token_address {
                            println!("🎯 找到匹配的交易数据!");
                            println!("   💰 池状态: {}", trading_data.pool_state);
                            println!("   🏦 Base Vault: {}", trading_data.pool_base_vault);
                            println!("   🏦 Quote Vault: {}", trading_data.pool_quote_vault);
                            println!("   📊 交易方向: {}", trading_data.trade_direction);
                            println!("   💵 原始输入: {} lamports", trading_data.amount_in);
                            println!("   💰 原始输出: {} lamports", trading_data.amount_out);
                            
                            // 立即执行买入
                            if let Err(e) = self.execute_buy(&trading_data).await {
                                eprintln!("❌ 买入失败: {}", e);
                            }
                            return Ok(());
                        } else {
                            println!("📡 收到其他Token: {} (继续等待目标Token)", trading_data.mint_address);
                        }
                    }
                    Err(e) => {
                        eprintln!("❌ 解析Redis消息失败: {}", e);
                        println!("📄 原始消息: {}", payload);
                    }
                }
            }
        }
    }

    pub async fn execute_buy(&self, trading_data: &TradingData) -> Result<Signature> {
        println!("🔄 根据池ID和公钥计算所有必要参数...");
        
        // 基础参数
        let pool_id = Pubkey::from_str(&trading_data.pool_state)?;
        let token_mint = Pubkey::from_str(&trading_data.mint_address)?;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        
        // 从Redis直接获取的金库地址
        let base_vault = Pubkey::from_str(&trading_data.pool_base_vault)?;
        let quote_vault = Pubkey::from_str(&trading_data.pool_quote_vault)?;
        
        // 计算池权限地址 (可以直接计算)
        let (pool_authority, _) = find_pool_authority(&pool_id);
        
        // 计算用户的关联Token账户地址 (可以直接计算)
        let user_wsol_account = get_associated_token_address(&self.keypair.pubkey(), &wsol_mint);
        let user_token_account = get_associated_token_address(&self.keypair.pubkey(), &token_mint);
        
        // 计算期望输出 (基于Redis数据的比例)
        let minimum_amount_out = if trading_data.amount_out > 0 {
            let ratio = self.wsol_amount as f64 / trading_data.amount_in as f64;
            let expected_out = ratio * trading_data.amount_out as f64;
            (expected_out * 0.95) as u64 // 5%滑点保护
        } else {
            1
        };
        
        println!("📊 计算出的交易参数:");
        println!("   🎯 Token: {}", token_mint);
        println!("   💰 Pool: {}", pool_id);
        println!("   🔑 Pool Authority: {}", pool_authority);
        println!("   🏦 Base Vault: {}", base_vault);
        println!("   🏦 Quote Vault: {}", quote_vault);
        println!("   👤 User WSOL: {}", user_wsol_account);
        println!("   👤 User Token: {}", user_token_account);
        println!("   💸 输入: {} lamports", self.wsol_amount);
        println!("   💰 最小输出: {} tokens", minimum_amount_out);
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(self.compute_limit));
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(self.priority_fee));
        
        // 2. 创建代币账户（幂等操作，已存在则跳过）
        println!("   🏗️ 确保WSOL关联账户存在");
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.keypair.pubkey(),
                &self.keypair.pubkey(),
                &wsol_mint,
                &spl_token::id(),
            )
        );
        
        println!("   🏗️ 确保Token关联账户存在");
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.keypair.pubkey(),
                &self.keypair.pubkey(),
                &token_mint,
                &spl_token::id(),
            )
        );
        
        // 3. SOL转到WSOL账户
        println!("   💰 包装SOL到WSOL");
        instructions.push(system_instruction::transfer(
            &self.keypair.pubkey(),
            &user_wsol_account,
            self.wsol_amount,
        ));
        
        // 4. 同步WSOL账户
        instructions.push(spl_token::instruction::sync_native(&spl_token::id(), &user_wsol_account)?);
        
        // 5. 构建Raydium Launchpad交易指令
        println!("🔥 构建Raydium Launchpad交易指令...");
        
        let raydium_launchpad_program = Pubkey::from_str("LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj")?;
        
        // Raydium Launchpad buy_exact_in指令数据
        let mut swap_data = vec![250, 234, 13, 123, 213, 156, 19, 236]; // 正确的buy_exact_in指令discriminator
        swap_data.extend_from_slice(&self.wsol_amount.to_le_bytes()); // amount_in
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes()); // minimum_amount_out  
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 计算Launchpad必要的PDA账户
        let launchpad_program_id = raydium_launchpad_program;
        let authority_seed = b"vault_auth_seed";
        let event_authority_seed = b"__event_authority";
        
        let (authority, _) = Pubkey::find_program_address(&[authority_seed], &launchpad_program_id);
        let (event_authority, _) = Pubkey::find_program_address(&[event_authority_seed], &launchpad_program_id);
        
        // 使用真实的固定配置地址
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        println!("📋 使用的关键地址:");
        println!("   🎯 Global Config: {}", global_config);
        println!("   📱 Platform Config: {}", platform_config);
        println!("   🔑 Authority: {}", authority);
        println!("   📅 Event Authority: {}", event_authority);
        
        // 构建Raydium Launchpad 15个账户列表
        let accounts = vec![
            // 0. payer (user wallet)
            solana_sdk::instruction::AccountMeta::new_readonly(self.keypair.pubkey(), true),
            // 1. authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(authority, false),
            // 2. global_config
            solana_sdk::instruction::AccountMeta::new_readonly(global_config, false),
            // 3. platform_config
            solana_sdk::instruction::AccountMeta::new_readonly(platform_config, false),
            // 4. pool_state (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(pool_id, false),
            // 5. user_base_token (用户Token账户)
            solana_sdk::instruction::AccountMeta::new(user_token_account, false),
            // 6. user_quote_token (用户WSOL账户)
            solana_sdk::instruction::AccountMeta::new(user_wsol_account, false),
            // 7. base_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(base_vault, false),
            // 8. quote_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(quote_vault, false),
            // 9. base_token_mint (Token mint)
            solana_sdk::instruction::AccountMeta::new_readonly(token_mint, false),
            // 10. quote_token_mint (WSOL mint)
            solana_sdk::instruction::AccountMeta::new_readonly(wsol_mint, false),
            // 11. base_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 12. quote_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 13. event_authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(event_authority, false),
            // 14. program (Raydium Launchpad)
            solana_sdk::instruction::AccountMeta::new_readonly(raydium_launchpad_program, false),
        ];
        
        instructions.push(Instruction {
            program_id: raydium_launchpad_program,
            accounts,
            data: swap_data,
        });
        
        // 6. 买入成功后立即关闭WSOL账户赎回租金（原子操作）
        println!("   🗑️ 添加关闭WSOL账户指令（原子执行）");
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.keypair.pubkey(), // 租金接收者
            &self.keypair.pubkey(), // 账户所有者
            &[],
        )?);
        
        println!("🚀 发送原子交易（买入+关闭WSOL账户）...");
        let signature = self.build_and_send_transaction(instructions).await?;
        
        println!("✅ 原子交易成功! 已自动赎回WSOL租金!");
        println!("   🔗 交易签名: {}", signature);
        println!("   🎯 购买Token: {}", token_mint);
        println!("   💸 花费: {} SOL", self.wsol_amount as f64 / 1_000_000_000.0);
        println!("   💰 使用池: {}", pool_id);
        println!("   💵 已赎回WSOL账户租金: 0.******** SOL");
        
        Ok(signature)
    }

    pub async fn manual_sell(&self, token_address: &str) -> Result<()> {
        println!("🔍 准备卖出Token: {} (全部余额)", token_address);
        
        // 获取用户Token账户余额
        let token_mint = Pubkey::from_str(token_address)?;
        let user_token_account = get_associated_token_address(&self.keypair.pubkey(), &token_mint);
        
        // 检查Token账户是否存在和余额
        let account_info = match self.rpc_client.get_account(&user_token_account) {
            Ok(account) => account,
            Err(_) => {
                println!("❌ 您没有这个Token的账户或余额为0");
                return Ok(());
            }
        };
        
        // 解析Token账户数据获取余额
        let token_amount = if account_info.data.len() >= 64 {
            u64::from_le_bytes(
                account_info.data[64..72].try_into()
                    .map_err(|_| anyhow::anyhow!("Failed to parse token amount"))?
            )
        } else {
            println!("❌ 无法读取Token账户数据");
            return Ok(());
        };
        
        if token_amount == 0 {
            println!("❌ Token余额为0，无法卖出");
            return Ok(());
        }
        
        println!("💰 当前Token余额: {} tokens", token_amount);
        println!("🔍 订阅Redis频道等待Token: {} 的交易数据...", token_address);
        
        // 创建新的Redis连接
        let client = redis::Client::open(self.redis_client.get_connection_info().clone())?;
        let mut con = client.get_async_connection().await?;
        let mut pubsub = con.into_pubsub();
        
        // 订阅所有交易事件频道
        pubsub.subscribe("trade_events:all").await?;
        println!("✅ 已订阅Redis频道: trade_events:all");
        println!("📡 等待Token {} 的交易数据...", token_address);
        
        // 监听消息
        let mut stream = pubsub.on_message();
        
        loop {
            if let Some(msg) = stream.next().await {
                let payload: String = msg.get_payload()?;
                
                match serde_json::from_str::<TradingData>(&payload) {
                    Ok(trading_data) => {
                        // 跳过mint_address为"unknown"的消息
                        if trading_data.mint_address == "unknown" {
                            println!("⏭️ 跳过未知Token地址的消息");
                            continue;
                        }
                        
                        if trading_data.mint_address == token_address {
                            println!("🎯 找到匹配的交易数据!");
                            println!("   💰 池状态: {}", trading_data.pool_state);
                            println!("   🏦 Base Vault: {}", trading_data.pool_base_vault);
                            println!("   🏦 Quote Vault: {}", trading_data.pool_quote_vault);
                            println!("   📊 交易方向: {}", trading_data.trade_direction);
                            
                            // 立即执行卖出
                            if let Err(e) = self.execute_sell(&trading_data, token_amount).await {
                                eprintln!("❌ 卖出失败: {}", e);
                            }
                            return Ok(());
                        } else {
                            println!("📡 收到其他Token: {} (继续等待目标Token)", trading_data.mint_address);
                        }
                    }
                    Err(e) => {
                        eprintln!("❌ 解析Redis消息失败: {}", e);
                        println!("📄 原始消息: {}", payload);
                    }
                }
            }
        }
    }

    pub async fn execute_sell(&self, trading_data: &TradingData, token_amount: u64) -> Result<Signature> {
        println!("🔄 准备卖出所有Token余额...");
        
        // 基础参数
        let pool_id = Pubkey::from_str(&trading_data.pool_state)?;
        let token_mint = Pubkey::from_str(&trading_data.mint_address)?;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        
        // 从Redis直接获取的金库地址
        let base_vault = Pubkey::from_str(&trading_data.pool_base_vault)?;
        let quote_vault = Pubkey::from_str(&trading_data.pool_quote_vault)?;
        
        // 计算池权限地址
        let (pool_authority, _) = find_pool_authority(&pool_id);
        
        // 计算用户的关联Token账户地址
        let user_wsol_account = get_associated_token_address(&self.keypair.pubkey(), &wsol_mint);
        let user_token_account = get_associated_token_address(&self.keypair.pubkey(), &token_mint);
        
        // 计算期望输出 (基于Redis数据的比例)
        let minimum_amount_out = if trading_data.amount_in > 0 && trading_data.amount_out > 0 {
            let ratio = token_amount as f64 / trading_data.amount_in as f64;
            let expected_out = ratio * trading_data.amount_out as f64;
            (expected_out * 0.95) as u64 // 5%滑点保护
        } else {
            1
        };
        
        println!("📊 计算出的卖出参数:");
        println!("   🎯 Token: {}", token_mint);
        println!("   💰 Pool: {}", pool_id);
        println!("   🔑 Pool Authority: {}", pool_authority);
        println!("   🏦 Base Vault: {}", base_vault);
        println!("   🏦 Quote Vault: {}", quote_vault);
        println!("   👤 User WSOL: {}", user_wsol_account);
        println!("   👤 User Token: {}", user_token_account);
        println!("   💸 卖出数量: {} tokens", token_amount);
        println!("   💰 最小获得: {} lamports", minimum_amount_out);
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(self.compute_limit));
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(self.priority_fee));
        
        // 2. 创建WSOL账户（如果不存在，用于接收卖出的SOL）
        println!("   🏗️ 确保WSOL关联账户存在");
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.keypair.pubkey(),
                &self.keypair.pubkey(),
                &wsol_mint,
                &spl_token::id(),
            )
        );
        
        // 3. 构建Raydium Launchpad卖出指令
        println!("🔥 构建Raydium Launchpad卖出指令...");
        
        let raydium_launchpad_program = Pubkey::from_str("LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj")?;
        
        // Raydium Launchpad sell_exact_in指令数据
        let mut swap_data = vec![149, 39, 222, 155, 211, 124, 152, 26]; // 正确的sell_exact_in指令discriminator
        swap_data.extend_from_slice(&token_amount.to_le_bytes()); // amount_in (全部Token)
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes()); // minimum_amount_out  
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 计算Launchpad必要的PDA账户
        let launchpad_program_id = raydium_launchpad_program;
        let authority_seed = b"vault_auth_seed";
        let event_authority_seed = b"__event_authority";
        
        let (authority, _) = Pubkey::find_program_address(&[authority_seed], &launchpad_program_id);
        let (event_authority, _) = Pubkey::find_program_address(&[event_authority_seed], &launchpad_program_id);
        
        // 使用真实的固定配置地址
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        println!("📋 使用的关键地址:");
        println!("   🎯 Global Config: {}", global_config);
        println!("   📱 Platform Config: {}", platform_config);
        println!("   🔑 Authority: {}", authority);
        println!("   📅 Event Authority: {}", event_authority);
        
        // 构建Raydium Launchpad 15个账户列表（卖出顺序）
        let accounts = vec![
            // 0. payer (user wallet)
            solana_sdk::instruction::AccountMeta::new_readonly(self.keypair.pubkey(), true),
            // 1. authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(authority, false),
            // 2. global_config
            solana_sdk::instruction::AccountMeta::new_readonly(global_config, false),
            // 3. platform_config
            solana_sdk::instruction::AccountMeta::new_readonly(platform_config, false),
            // 4. pool_state (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(pool_id, false),
            // 5. user_base_token (用户Token账户) - 卖出源
            solana_sdk::instruction::AccountMeta::new(user_token_account, false),
            // 6. user_quote_token (用户WSOL账户) - 接收WSOL
            solana_sdk::instruction::AccountMeta::new(user_wsol_account, false),
            // 7. base_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(base_vault, false),
            // 8. quote_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(quote_vault, false),
            // 9. base_token_mint (Token mint)
            solana_sdk::instruction::AccountMeta::new_readonly(token_mint, false),
            // 10. quote_token_mint (WSOL mint)
            solana_sdk::instruction::AccountMeta::new_readonly(wsol_mint, false),
            // 11. base_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 12. quote_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 13. event_authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(event_authority, false),
            // 14. program (Raydium Launchpad)
            solana_sdk::instruction::AccountMeta::new_readonly(raydium_launchpad_program, false),
        ];
        
        instructions.push(Instruction {
            program_id: raydium_launchpad_program,
            accounts,
            data: swap_data,
        });
        
        // 4. 卖出成功后unwrap WSOL为SOL
        println!("   💰 添加unwrap WSOL指令");
        instructions.push(spl_token::instruction::sync_native(&spl_token::id(), &user_wsol_account)?);
        
        // 5. 立即关闭WSOL账户赎回租金（原子操作）
        println!("   🗑️ 添加关闭WSOL账户指令（原子执行）");
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.keypair.pubkey(), // 租金接收者
            &self.keypair.pubkey(), // 账户所有者
            &[],
        )?);
        
        // 6. 关闭空的Token账户赎回租金
        println!("   🗑️ 添加关闭Token账户指令（原子执行）");
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_token_account,
            &self.keypair.pubkey(), // 租金接收者
            &self.keypair.pubkey(), // 账户所有者
            &[],
        )?);
        
        println!("🚀 发送原子卖出交易（卖出+关闭账户+赎回租金）...");
        let signature = self.build_and_send_transaction(instructions).await?;
        
        println!("✅ 卖出交易成功! 已赎回所有租金!");
        println!("   🔗 交易签名: {}", signature);
        println!("   🎯 卖出Token: {}", token_mint);
        println!("   💸 卖出数量: {} tokens", token_amount);
        println!("   💰 使用池: {}", pool_id);
        println!("   💵 已赎回租金: ~0.******** SOL (WSOL+Token账户)");
        
        Ok(signature)
    }

    pub async fn start_interactive_mode(&self) -> Result<()> {
        println!("🚀 开始交互模式");
        println!("💰 钱包地址: {}", self.keypair.pubkey());
        
        let balance = self.get_balance()?;
        println!("💰 当前余额: {} SOL", balance as f64 / 1_000_000_000.0);
        
        if balance < self.wsol_amount + 5_000_000 {
            println!("⚠️ 余额不足，需要至少 {} SOL", (self.wsol_amount + 5_000_000) as f64 / 1_000_000_000.0);
            return Ok(());
        }
        
        loop {
            println!("\n📋 请选择操作:");
            println!("1. 💰 买入 (Buy)");
            println!("2. 💸 卖出 (Sell)");
            println!("3. 🚪 退出 (Quit)");
            print!("请输入选择 (1/2/3): ");
            io::stdout().flush()?;
            
            let mut choice = String::new();
            io::stdin().read_line(&mut choice)?;
            let choice = choice.trim();
            
            match choice {
                "1" => {
                    print!("📝 请输入要买入的Token地址: ");
                    io::stdout().flush()?;
                    
                    let mut token_address = String::new();
                    io::stdin().read_line(&mut token_address)?;
                    let token_address = token_address.trim();
                    
                    if let Err(_) = Pubkey::from_str(token_address) {
                        println!("❌ 无效的Token地址格式");
                        continue;
                    }
                    
                    self.manual_buy(token_address).await?;
                }
                "2" => {
                    print!("📝 请输入要卖出的Token地址: ");
                    io::stdout().flush()?;
                    
                    let mut token_address = String::new();
                    io::stdin().read_line(&mut token_address)?;
                    let token_address = token_address.trim();
                    
                    if let Err(_) = Pubkey::from_str(token_address) {
                        println!("❌ 无效的Token地址格式");
                        continue;
                    }
                    
                    self.manual_sell(token_address).await?;
                }
                "3" => {
                    println!("👋 再见!");
                    break;
                }
                _ => {
                    println!("❌ 无效选择，请输入 1、2 或 3");
                }
            }
        }
        
        Ok(())
    }

    async fn build_and_send_transaction(&self, instructions: Vec<Instruction>) -> Result<Signature> {
        let recent_blockhash = self.rpc_client.get_latest_blockhash()?;
        let message = Message::new(&instructions, Some(&self.keypair.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&self.keypair], recent_blockhash);
        
        // 先模拟交易，显示完整错误信息
        println!("🔍 模拟交易...");
        match self.rpc_client.simulate_transaction(&transaction) {
            Ok(result) => {
                let has_error = result.value.err.is_some();
                
                if let Some(err) = result.value.err {
                    println!("❌ 交易模拟失败:");
                    println!("   错误: {:?}", err);
                }
                if let Some(logs) = result.value.logs {
                    println!("📋 交易日志:");
                    for (i, log) in logs.iter().enumerate() {
                        println!("   {}: {}", i + 1, log);
                    }
                }
                if has_error {
                    return Err(anyhow::anyhow!("交易模拟失败"));
                }
            }
            Err(e) => {
                println!("❌ 模拟交易失败: {}", e);
                return Err(e.into());
            }
        }
        
        // 如果模拟成功，发送交易
        println!("✅ 模拟成功，发送真实交易...");
        let signature = self.rpc_client.send_and_confirm_transaction(&transaction)?;
        Ok(signature)
    }

    pub fn get_balance(&self) -> Result<u64> {
        let balance = self.rpc_client.get_balance(&self.keypair.pubkey())?;
        Ok(balance)
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("🤖 真实交易复刻机器人启动!");
    println!("💰 固定交易金额: 0.001 SOL");
    println!("🔥 构建真实Raydium AMM交易指令");
    println!("📋 包含: WSOL包装、Token账户创建、真实交易指令结构");
    
    let redis_url = "redis://127.0.0.1:6379";
    let rpc_url = "https://api.mainnet-beta.solana.com";
    let private_key = "4r3bGyiMWsJTSVi8xA9JbFVsvb6z67Xjq3hshMh9vaqWS8S1RFb1xrxYT2otvNXMwqZwJRC7x3M1GzZ1sRd84LBY"; // 使用提供的公钥
    let priority_fee = 100_000;
    let compute_limit = 600_000;
    let redis_channel = "trading_data";
    
    let mut bot = TradingBot::new(redis_url, rpc_url, private_key, priority_fee, compute_limit)?;
    bot.start_interactive_mode().await?;
    
    Ok(())
}

// 测试用的Redis数据发送函数
#[allow(dead_code)]
pub async fn send_test_data(redis_url: &str, channel: &str) -> Result<()> {
    let client = Client::open(redis_url)?;
    let mut con = client.get_connection()?;
    
    let test_data = TradingData {
        signature: "128cQyvSR7aY8vduD8SRrJeHwZE6WtAFMd1jkQkpr9WKFrzFGu81sKS8DCjC9vRvheDZL1CpgCfR99rGJHvNjsC8".to_string(),
        pool_state: "95ToZchiWFZJfb2Qh67oziCBeZnDuYFDvA9Sbwm74qMC".to_string(),
        signer: "BakiByJHqyCgRstfTw7eqTo3XZX719Vm9fuMyrtwM2TT".to_string(),
        mint_address: "Eouhxf9NNekHvYRKkUikF5TTobziwmGH3p7X2ALGbonk".to_string(),
        total_base_sell: 793100000000000,
        virtual_base: 1073025605596382,
        virtual_quote: 30000852951,
        real_base_before: 390448807058069,
        real_quote_before: 17161141899,
        real_base_after: 389390366454416,
        real_quote_after: 17088123179,
        amount_in: 1058440603653,
        amount_out: 72105986,
        protocol_fee: 182547,
        platform_fee: 730187,
        share_fee: 0,
        trade_direction: "Sell".to_string(),
        pool_status: "Fund".to_string(),
        price_before: 4.3952348140860706e-8,
        price_after: 4.38842987683426e-8,
        slippage: -0.15482534016161664,
        pool_base_vault: "9CJ5fZH4pGVp34uWgju7zqz8hK8RkMFeuSMZPrfA5yGU".to_string(),
        pool_quote_vault: "HEs39434tGNKDQCLCiE7GUVmj3BMCLrxv66FvNs8spKr".to_string(),
    };
    
    let json_data = serde_json::to_string(&test_data)?;
    let _: () = con.set(channel, json_data)?;
    
    println!("✅ 测试数据已发送到Redis频道: {}", channel);
    Ok(())
}
