[package]
name = "mirror-admin-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = "0.7.5"
tower-http = { version = "0.5.2", features = ["cors"] }
tokio = { version = "1.37.0", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = "0.3"

# 工具库
anyhow = "1.0"
chrono = { version = "0.4", features = ["serde"] }

# 并发存储
dashmap = "5.5" 