<template>
  <div class="config-page">
    <div class="page-title">
      <h2>配置管理</h2>
      <p>管理系统全局配置参数</p>
    </div>

    <el-row :gutter="20">
      <!-- 手续费配置 -->
      <el-col :span="12">
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">手续费配置</h3>
            <el-button type="primary" @click="saveFeeConfig">保存配置</el-button>
          </div>
          
          <el-form :model="feeConfig" label-width="120px" class="form-container">
            <el-form-item label="默认费率">
              <el-input-number 
                v-model="feeConfig.defaultRate" 
                :min="0" 
                :max="100" 
                :precision="2"
                controls-position="right"
              />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
            
            <el-form-item label="手续费地址池">
              <div v-for="(address, index) in feeConfig.addresses" :key="index" style="margin-bottom: 8px;">
                <el-input v-model="feeConfig.addresses[index]" placeholder="输入钱包地址">
                  <template #append>
                    <el-button @click="removeAddress(index)" type="danger" size="small">删除</el-button>
                  </template>
                </el-input>
              </div>
              <el-button @click="addAddress" type="primary" size="small">添加地址</el-button>
            </el-form-item>
            
            <el-form-item label="轮换间隔">
              <el-input-number 
                v-model="feeConfig.rotationInterval" 
                :min="1" 
                :max="60"
                controls-position="right"
              />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
          </el-form>
        </div>
      </el-col>

      <!-- 时间配置 -->
      <el-col :span="12">
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">时间限制配置</h3>
            <el-button type="primary" @click="saveTimeConfig">保存配置</el-button>
          </div>
          
          <el-form :model="timeConfig" label-width="120px" class="form-container">
            <el-form-item label="默认日限时间">
              <el-input-number 
                v-model="timeConfig.defaultDailyHours" 
                :min="1" 
                :max="24"
                controls-position="right"
              />
              <span style="margin-left: 8px;">小时</span>
            </el-form-item>
            
            <el-form-item label="全局时间段">
              <el-time-picker
                v-model="timeConfig.globalTimeRange"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
            
            <el-form-item label="强制重启">
              <el-switch v-model="timeConfig.forceRestart" />
              <span style="margin-left: 8px; color: #666;">重启后无法绕过时间限制</span>
            </el-form-item>
            
            <el-form-item label="心跳超时">
              <el-input-number 
                v-model="timeConfig.heartbeatTimeout" 
                :min="30" 
                :max="300"
                controls-position="right"
              />
              <span style="margin-left: 8px;">秒</span>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>

    <!-- 高级配置 -->
    <div class="content-card">
      <div class="card-header">
        <h3 class="card-title">高级配置</h3>
        <el-button type="primary" @click="saveAdvancedConfig">保存配置</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form :model="advancedConfig" label-width="120px">
            <el-form-item label="数据上传间隔">
              <el-input-number 
                v-model="advancedConfig.uploadInterval" 
                :min="1" 
                :max="60"
                controls-position="right"
              />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
            
            <el-form-item label="日志级别">
              <el-select v-model="advancedConfig.logLevel">
                <el-option label="DEBUG" value="debug" />
                <el-option label="INFO" value="info" />
                <el-option label="WARN" value="warn" />
                <el-option label="ERROR" value="error" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
        
        <el-col :span="8">
          <el-form :model="advancedConfig" label-width="120px">
            <el-form-item label="最大重试次数">
              <el-input-number 
                v-model="advancedConfig.maxRetries" 
                :min="1" 
                :max="10"
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="连接超时">
              <el-input-number 
                v-model="advancedConfig.connectionTimeout" 
                :min="5" 
                :max="60"
                controls-position="right"
              />
              <span style="margin-left: 8px;">秒</span>
            </el-form-item>
          </el-form>
        </el-col>
        
        <el-col :span="8">
          <el-form :model="advancedConfig" label-width="120px">
            <el-form-item label="启用加密">
              <el-switch v-model="advancedConfig.enableEncryption" />
            </el-form-item>
            
            <el-form-item label="自动备份">
              <el-switch v-model="advancedConfig.autoBackup" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { adminAPI } from '@/api'

// 手续费配置
const feeConfig = reactive({
  defaultRate: 0,
  addresses: [],
  rotationInterval: 15
})

// 时间配置
const timeConfig = reactive({
  defaultDailyHours: 8,
  globalTimeRange: [],
  forceRestart: false,
  heartbeatTimeout: 60
})

// 高级配置
const advancedConfig = reactive({
  uploadInterval: 5,
  logLevel: 'info',
  maxRetries: 3,
  connectionTimeout: 30,
  enableEncryption: true,
  autoBackup: false
})

// 加载配置数据
const loadConfig = async () => {
  try {
    const result = await adminAPI.config.getGlobalConfig()
    if (result.success && result.data) {
      const config = result.data
      // 映射后端配置到前端结构
      feeConfig.defaultRate = config.fee_rate
      feeConfig.addresses = [...config.fee_addresses]
      timeConfig.defaultDailyHours = config.max_daily_hours
    }
  } catch (error) {
    ElMessage.error('加载配置失败: ' + error.message)
  }
}

// 页面加载时获取配置
onMounted(() => {
  loadConfig()
})

// 添加地址
const addAddress = () => {
  feeConfig.addresses.push('')
}

// 删除地址
const removeAddress = (index) => {
  if (feeConfig.addresses.length > 1) {
    feeConfig.addresses.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个手续费地址')
  }
}

// 保存手续费配置
const saveFeeConfig = async () => {
  try {
    // 验证地址不能为空
    const validAddresses = feeConfig.addresses.filter(addr => addr.trim() !== '')
    if (validAddresses.length === 0) {
      ElMessage.warning('请至少添加一个有效的手续费地址')
      return
    }

    const configData = {
      fee_addresses: validAddresses,
      fee_rate: feeConfig.defaultRate,
      max_daily_hours: timeConfig.defaultDailyHours
    }

    const result = await adminAPI.config.updateGlobalConfig(configData)
    if (result.success) {
      ElMessage.success('手续费配置已保存')
      await loadConfig() // 重新加载配置
    }
  } catch (error) {
    ElMessage.error('保存配置失败: ' + error.message)
  }
}

// 保存时间配置
const saveTimeConfig = async () => {
  try {
    const configData = {
      fee_addresses: feeConfig.addresses.filter(addr => addr.trim() !== ''),
      fee_rate: feeConfig.defaultRate,
      max_daily_hours: timeConfig.defaultDailyHours
    }

    const result = await adminAPI.config.updateGlobalConfig(configData)
    if (result.success) {
      ElMessage.success('时间配置已保存')
      await loadConfig() // 重新加载配置
    }
  } catch (error) {
    ElMessage.error('保存配置失败: ' + error.message)
  }
}

// 保存高级配置
const saveAdvancedConfig = () => {
  // 高级配置暂时不与后端连接，只是本地设置
  ElMessage.success('高级配置已保存')
}
</script>

<style scoped>
.config-page {
  padding: 0;
}

.page-title {
  margin-bottom: 24px;
}

.page-title h2 {
  font-size: 20px;
  color: #333;
  margin: 0 0 4px 0;
}

.page-title p {
  color: #666;
  font-size: 14px;
  margin: 0;
}
</style> 