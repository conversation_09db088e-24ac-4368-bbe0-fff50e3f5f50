use anyhow::{anyhow, Result};
use async_trait::async_trait;
use reqwest::{header::CONTENT_TYPE, Client};
use solana_sdk::transaction::Transaction;
use std::cell::RefCell;
use base64::engine::general_purpose::STANDARD as B64_ENGINE;
use base64::Engine;
use uuid::Uuid;
use std::fmt::Write;

use super::TransactionSenderBackend;

thread_local! {
    static SER_BUF: RefCell<Vec<u8>> = RefCell::new(Vec::with_capacity(2048));
    static B64_BUF: RefCell<String> = RefCell::new(String::with_capacity(4096));
    static JSON_BUF: RefCell<String> = RefCell::new(String::with_capacity(8192));
}

pub struct RpcSender {
    rpc_url: String,
    client: Client,
    skip_preflight: bool,
}

impl RpcSender {
    pub fn new(rpc_url: String, client: Client, skip_preflight: bool) -> Self {
        Self {
            rpc_url,
            client,
            skip_preflight,
        }
    }
}

#[async_trait]
impl TransactionSenderBackend for RpcSender {
    async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        let tx_base64 = SER_BUF.with(|ser_cell| {
            B64_BUF.with(|b64_cell| {
                let mut ser_buf = ser_cell.borrow_mut();
                ser_buf.clear();
                bincode::serialize_into(&mut *ser_buf, tx)
                    .map_err(|e| anyhow!("bincode 序列化失败: {e}"))?;
                let mut b64_buf = b64_cell.borrow_mut();
                b64_buf.clear();
                B64_ENGINE.encode_string(ser_buf.as_slice(), &mut *b64_buf);
                Ok::<_, anyhow::Error>(b64_buf.clone())
            })
        })?;

        let dynamic_skip_preflight = if tracing::level_enabled!(tracing::Level::DEBUG) {
            false
        } else {
            self.skip_preflight
        };

        let uuid = Uuid::new_v4();
        let body_string = JSON_BUF.with(|json_cell| {
            let mut json_buf = json_cell.borrow_mut();
            json_buf.clear();
            write!(
                &mut *json_buf,
                "{{\"jsonrpc\":\"2.0\",\"id\":\"{}\",\"method\":\"sendTransaction\",\"params\":[\"{}\",{{\"encoding\":\"base64\",\"skipPreflight\":{},\"preflightCommitment\":\"processed\",\"maxRetries\":1}}]}}",
                uuid,
                tx_base64,
                dynamic_skip_preflight
            )
            .expect("写入 JSON 失败");
            Ok::<_, anyhow::Error>(json_buf.clone())
        })?;

        let resp = self
            .client
            .post(&self.rpc_url)
            .header(CONTENT_TYPE, "application/json")
            .body(body_string)
            .send()
            .await?;
        
        let status = resp.status();
        let text = resp.text().await?;

        if !status.is_success() {
            return Err(anyhow!("HTTP状态非成功: {} – {}", status, text));
        }

        let v: serde_json::Value = serde_json::from_str(&text)?;
        tracing::debug!("RPC原始响应: {}", v);

        if v.get("error").is_some() {
            return Err(anyhow!("RPC返回错误: {}", v["error"]));
        }

        let sig_str = v
            .get("result")
            .and_then(|r| r.as_str())
            .ok_or_else(|| anyhow!("RPC响应中缺少result字段"))?;
        
        Ok(sig_str.to_string())
    }
} 