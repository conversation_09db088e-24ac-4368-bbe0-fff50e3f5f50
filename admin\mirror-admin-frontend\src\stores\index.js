import { defineStore } from 'pinia'

// 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: localStorage.getItem('admin_token') || '',
    isLoggedIn: false
  }),
  
  actions: {
    // 登录
    login(token, userInfo) {
      this.token = token
      this.userInfo = userInfo
      this.isLoggedIn = true
      localStorage.setItem('admin_token', token)
    },
    
    // 登出
    logout() {
      this.token = ''
      this.userInfo = null
      this.isLoggedIn = false
      localStorage.removeItem('admin_token')
    },
    
    // 检查登录状态
    checkAuth() {
      const token = localStorage.getItem('admin_token')
      if (token) {
        this.token = token
        this.isLoggedIn = true
        // 可以在这里调用API验证token有效性
      }
    }
  }
})

// 镜像端状态管理
export const useMirrorStore = defineStore('mirror', {
  state: () => ({
    mirrors: [],
    activeMirrors: 0,
    totalMirrors: 0,
    loading: false
  }),
  
  actions: {
    // 获取镜像列表
    async fetchMirrors() {
      this.loading = true
      try {
        // TODO: 调用API获取镜像数据
        // const response = await api.getMirrors()
        // this.mirrors = response.data
        this.loading = false
      } catch (error) {
        console.error('获取镜像列表失败:', error)
        this.loading = false
      }
    },
    
    // 更新镜像状态
    updateMirrorStatus(mirrorId, status) {
      const mirror = this.mirrors.find(m => m.id === mirrorId)
      if (mirror) {
        mirror.status = status
      }
    }
  }
})

// 配置状态管理
export const useConfigStore = defineStore('config', {
  state: () => ({
    feeConfig: {
      addresses: [],
      rates: {},
    },
    timeConfig: {
      maxDailyHours: 8,
      allowedTimeRanges: []
    }
  }),
  
  actions: {
    // 更新手续费配置
    updateFeeConfig(config) {
      this.feeConfig = { ...this.feeConfig, ...config }
    },
    
    // 更新时间配置
    updateTimeConfig(config) {
      this.timeConfig = { ...this.timeConfig, ...config }
    }
  }
}) 