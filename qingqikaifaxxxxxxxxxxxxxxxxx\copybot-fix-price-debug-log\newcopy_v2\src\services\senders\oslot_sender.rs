use anyhow::{anyhow, Result};
use async_trait::async_trait;
use reqwest::{header::CONTENT_TYPE, Client};
use serde::Deserialize;
use serde_json::json;
use solana_sdk::transaction::Transaction;
use std::cell::RefCell;
use base64::engine::general_purpose::STANDARD as B64_ENGINE;
use base64::Engine;
use tracing::{error, info, warn};
use rand::seq::SliceRandom;
use uuid;

use crate::config::AcceleratorConfig;
use super::{TransactionSenderBackend, rpc_sender::RpcSender};

// --- Oslot (0slot) 硬编码的小费地址 ---
const OSLOT_TIP_ADDRESSES: [&str; 15] = [
    "6fQaVhYZA4w3MBSXjJ81Vf6W1EDYeUPXpgVQ6UQyU1Av",
    "4HiwLEP2Bzqj3hM2ENxJuzhcPCdsafwiet3oGkMkuQY4",
    "7toBU3inhmrARGngC7z6SjyP85HgGMmCTEwGNRAcYnEK",
    "8mR3wB1nh4D6J9RUCugxUpc6ya8w38LPxZ3ZjcBhgzws",
    "6SiVU5WEwqfFapRuYCndomztEwDjvS5xgtEof3PLEGm9",
    "TpdxgNJBWZRL8UXF5mrEsyWxDWx9HQexA9P1eTWQ42p",
    "D8f3WkQu6dCF33cZxuAsrKHrGsqGP2yvAHf8mX6RXnwf",
    "GQPFicsy3P3NXxB5piJohoxACqTvWE9fKpLgdsMduoHE",
    "Ey2JEr8hDkgN8qKJGrLf2yFjRhW7rab99HVxwi5rcvJE",
    "4iUgjMT8q2hNZnLuhpqZ1QtiV8deFPy2ajvvjEpKKgsS",
    "3Rz8uD83QsU8wKvZbgWAPvCNDU6Fy8TSZTMcPm3RB6zt",
    "DiTmWENJsHQdawVUUKnUXkconcpW4Jv52TnMWhkncF6t",
    "HRyRhQ86t3H4aAtgvHVpUJmw64BDrb61gRiKcdKUXs5c",
    "7y4whZmw388w1ggjToDLSBLv47drw5SUXcLk6jtmwixd",
    "J9BMEWFbCBEjtQ1fG5Lo9kouX1HfrKQxeUxetwXrifBw",
];

thread_local! {
    static SER_BUF: RefCell<Vec<u8>> = RefCell::new(Vec::with_capacity(2048));
    static B64_BUF: RefCell<String> = RefCell::new(String::with_capacity(4096));
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
struct OslotData {
    signatures: Vec<String>,
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
struct OslotResponse {
    success: bool,
    data: Option<OslotData>,
    message: Option<String>,
}

pub struct OslotSender {
    client: Client,
    config: AcceleratorConfig,
    fallback_sender: RpcSender,
}

impl OslotSender {
    pub fn new(client: Client, config: AcceleratorConfig, fallback_sender: RpcSender) -> Self {
        Self {
            client,
            config,
            fallback_sender,
        }
    }

    pub fn get_random_tip_account() -> &'static str {
        let mut rng = rand::thread_rng();
        OSLOT_TIP_ADDRESSES.choose(&mut rng).unwrap()
    }
}

#[async_trait]
impl TransactionSenderBackend for OslotSender {
    async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        // 从配置中获取激活的提供商URL和Key
        let (api_url, api_key) = match self.config.get_active_provider_config() {
            Some(config) => config,
            None => {
                // 如果配置无效或未启用，直接回退
                warn!("Oslot/Flashblock 加速器未启用或提供商配置无效。将回退到标准RPC。");
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let tx_base64 = SER_BUF.with(|ser_cell| {
            B64_BUF.with(|b64_cell| {
                let mut ser_buf = ser_cell.borrow_mut();
                ser_buf.clear();
                bincode::serialize_into(&mut *ser_buf, tx)
                    .map_err(|e| anyhow!("bincode 序列化失败: {e}"))?;
                let mut b64_buf = b64_cell.borrow_mut();
                b64_buf.clear();
                B64_ENGINE.encode_string(ser_buf.as_slice(), &mut *b64_buf);
                Ok::<_, anyhow::Error>(b64_buf.clone())
            })
        })?;

        // 构造 JSON-RPC 请求体
        let request_id = uuid::Uuid::new_v4().to_string();
        let body = json!({
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "sendTransaction",
            "params": [ tx_base64, { "encoding": "base64" } ]
        });

        let url_with_key = if api_url.contains("api-key=") {
            api_url
        } else {
            let base = api_url.trim_end_matches('/') ;
            format!("{}?api-key={}", base, api_key)
        };

        let resp = self.client
            .post(&url_with_key)
            .header(CONTENT_TYPE, "application/json")
            .json(&body)
            .send()
            .await;
        
        let resp = match resp {
            Ok(r) => r,
            Err(e) => {
                error!("发送到Oslot时发生网络错误: {}。将回退到标准RPC。", e);
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let status = resp.status();
        let text = resp.text().await?;

        if !status.is_success() {
            warn!("Oslot 加速器HTTP状态非成功: {} – {}。将回退到标准RPC。", status, text);
            return self.fallback_sender.send_transaction(tx).await;
        }

        // 尝试解析 {"result": "<sig>"} 结构
        match serde_json::from_str::<serde_json::Value>(&text) {
            Ok(v) => {
                if let Some(sig) = v.get("result").and_then(|r| r.as_str()) {
                    info!("⚡️ 交易已通过 Oslot 加速器提交: {}", sig);
                    return Ok(sig.to_string());
                }
                error!("Oslot 返回成功但缺少 result 字段，原文: {}。回退RPC。", text);
                self.fallback_sender.send_transaction(tx).await
            }
            Err(e) => {
                error!("解析 Oslot JSON 失败: {}，原文: {}。回退RPC。", e, text);
                self.fallback_sender.send_transaction(tx).await
            }
        }
    }
} 