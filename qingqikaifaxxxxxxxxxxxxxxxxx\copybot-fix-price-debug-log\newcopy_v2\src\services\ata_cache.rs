use dashmap::DashMap;
use solana_sdk::pubkey::Pubkey;
use std::{sync::Arc, time::{Duration, Instant}};
use tokio::time::sleep;
use tracing::debug;

/// 维护已初始化ATA地址的线程安全缓存
#[derive(Clone)]
pub struct AtaCache {
    inner: Arc<DashMap<Pubkey, Instant>>, // 存储插入时间
    ttl: Duration,
}

impl AtaCache {
    pub fn new(ttl_secs: u64) -> Self {
        let cache = Self {
            inner: Arc::new(DashMap::new()),
            ttl: Duration::from_secs(ttl_secs),
        };
        cache.start_cleanup_task();
        cache
    }

    fn start_cleanup_task(&self) {
        let inner = self.inner.clone();
        let ttl = self.ttl;
        tokio::spawn(async move {
            loop {
                sleep(ttl).await;
                let now = Instant::now();
                let mut removed = 0;
                inner.retain(|_, ts| {
                    if now.duration_since(*ts) < ttl {
                        true
                    } else {
                        removed += 1;
                        false
                    }
                });
                if removed > 0 {
                    debug!("ATA缓存清理: 移除 {} 项", removed);
                }
            }
        });
    }

    /// 检查 ATA 是否缓存存在
    pub fn contains(&self, ata: &Pubkey) -> bool {
        self.inner.contains_key(ata)
    }

    /// 插入 ATA
    pub fn insert(&self, ata: Pubkey) {
        self.inner.insert(ata, Instant::now());
    }
} 