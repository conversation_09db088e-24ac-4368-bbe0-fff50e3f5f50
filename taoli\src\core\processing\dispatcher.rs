/// 多插件分发器
/// 
/// 负责将传入的交易分发到合适的插件解析器进行处理

use std::sync::Arc;
use std::time::Instant;

use anyhow::Result;
use log::{debug, info, error};

use crate::core::parsers::{ParserFactory, RawTransaction};

/// 零延迟交易分发器
pub struct TransactionDispatcher {
    factory: Arc<ParserFactory>,
    metrics: Arc<DispatcherMetrics>,
}

impl TransactionDispatcher {
    pub fn new(factory: Arc<ParserFactory>) -> Self {
        Self {
            factory,
            metrics: Arc::new(DispatcherMetrics::new()),
        }
    }
    
    /// 启动分发服务 - 零延迟处理
    pub async fn start(&self, mut rx: tokio::sync::mpsc::UnboundedReceiver<Arc<RawTransaction>>) -> Result<()> {
        info!("启动交易分发器");
        
        // 启动指标监控
        self.start_metrics_monitor();
        
        // 零延迟分发循环
        while let Some(transaction) = rx.recv().await {
            let start_time = std::time::Instant::now();
            
            // 更新接收计数
            self.metrics.received_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            
            // 零延迟分发 - 不等待处理完成
            self.factory.dispatch(transaction).await;
            
            // 更新分发计数
            self.metrics.dispatched_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            
            // 记录延迟
            let elapsed = start_time.elapsed();
            self.metrics.dispatch_time_ns.store(elapsed.as_nanos() as u64, std::sync::atomic::Ordering::Relaxed);
            
            debug!("分发完成，耗时: {}ns", elapsed.as_nanos());
        }
        
        info!("交易分发器已停止");
        Ok(())
    }
    
    /// 启动指标监控
    fn start_metrics_monitor(&self) {
        let metrics = Arc::clone(&self.metrics);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(30));
            let mut last_received = 0u64;
            let mut last_dispatched = 0u64;
            
            loop {
                interval.tick().await;
                
                let received = metrics.received_count.load(std::sync::atomic::Ordering::Relaxed);
                let dispatched = metrics.dispatched_count.load(std::sync::atomic::Ordering::Relaxed);
                let avg_time = metrics.dispatch_time_ns.load(std::sync::atomic::Ordering::Relaxed);
                
                let received_tps = (received - last_received) / 30;
                let dispatched_tps = (dispatched - last_dispatched) / 30;
                
                info!("📊 分发器指标 - 接收TPS: {}, 分发TPS: {}, 平均延迟: {}ns", 
                    received_tps, dispatched_tps, avg_time);
                
                last_received = received;
                last_dispatched = dispatched;
            }
        });
    }
    
    /// 获取指标
    pub fn get_metrics(&self) -> Arc<DispatcherMetrics> {
        Arc::clone(&self.metrics)
    }
}

/// 分发器指标
pub struct DispatcherMetrics {
    pub received_count: std::sync::atomic::AtomicU64,
    pub dispatched_count: std::sync::atomic::AtomicU64,
    pub dispatch_time_ns: std::sync::atomic::AtomicU64,
}

impl DispatcherMetrics {
    fn new() -> Self {
        Self {
            received_count: std::sync::atomic::AtomicU64::new(0),
            dispatched_count: std::sync::atomic::AtomicU64::new(0),
            dispatch_time_ns: std::sync::atomic::AtomicU64::new(0),
        }
    }
}

/// 启动分发服务
pub async fn start_dispatch_service(
    factory: Arc<ParserFactory>,
    rx: tokio::sync::mpsc::UnboundedReceiver<Arc<RawTransaction>>,
) -> Result<tokio::task::JoinHandle<Result<()>>> {
    let dispatcher = TransactionDispatcher::new(factory);
    
    let handle = tokio::spawn(async move {
        dispatcher.start(rx).await
    });
    
    Ok(handle)
}