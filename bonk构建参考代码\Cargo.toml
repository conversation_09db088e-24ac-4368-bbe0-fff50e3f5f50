[package]
name = "simple-trading-bot"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
redis = { version = "0.24", features = ["tokio-comp", "streams"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
solana-client = "1.18"
solana-sdk = "1.18"
spl-token = "4.0"
spl-associated-token-account = "2.3"
bs58 = "0.5"
anyhow = "1.0"
bincode = "1.3"
bytemuck = "1.13"
futures-util = "0.3"
