#!/usr/bin/env python3
"""
Redis发布订阅测试脚本
用于验证交易事件的实时发布功能
"""

import redis
import json
import threading
import time
import signal
import sys

class RedisPubSubTester:
    def __init__(self, redis_host='127.0.0.1', redis_port=6379):
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
        self.running = True
        
    def subscribe_to_all_trades(self):
        """订阅所有交易事件"""
        try:
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('trade_events:all')
            
            print("🔥 订阅所有交易事件频道...")
            print("等待交易事件...")
            print("-" * 60)
            
            for message in pubsub.listen():
                if not self.running:
                    break
                    
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        self.print_trade_event(data)
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        
        except Exception as e:
            print(f"❌ 订阅错误: {e}")
    
    def subscribe_to_specific_mint(self, mint_address):
        """订阅特定代币的交易事件"""
        try:
            pubsub = self.redis_client.pubsub()
            channel = f'trade_events:{mint_address}'
            pubsub.subscribe(channel)
            
            print(f"🎯 订阅特定代币事件: {mint_address}")
            print("等待该代币的交易事件...")
            print("-" * 60)
            
            for message in pubsub.listen():
                if not self.running:
                    break
                    
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        self.print_trade_event(data, specific=True)
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        
        except Exception as e:
            print(f"❌ 订阅错误: {e}")
    
    def print_trade_event(self, data, specific=False):
        """格式化打印交易事件"""
        prefix = "🎯" if specific else "🔥"
        direction = "🟢 买入" if data.get('trade_direction') == 'buy' else "🔴 卖出"
        
        print(f"{prefix} {direction} | 签名: {data.get('signature', 'unknown')[:16]}...")
        print(f"   💰 代币: {data.get('mint_address', 'unknown')}")
        print(f"   📈 输入: {data.get('amount_in', 0):,} | 输出: {data.get('amount_out', 0):,}")
        print(f"   💲 价格变化: {data.get('price_before', 0):.8f} → {data.get('price_after', 0):.8f}")
        print(f"   📊 滑点: {data.get('slippage', 0):.2f}%")
        print(f"   ⏰ 时间: {time.strftime('%H:%M:%S', time.localtime(data.get('timestamp', 0)/1000))}")
        print("-" * 60)
    
    def test_connection(self):
        """测试Redis连接"""
        try:
            self.redis_client.ping()
            print("✅ Redis连接成功!")
            return True
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            return False
    
    def stop(self):
        """停止监听"""
        self.running = False
        print("\n🛑 停止监听...")

def signal_handler(signum, frame, tester):
    """处理Ctrl+C信号"""
    tester.stop()
    sys.exit(0)

def main():
    print("🚀 Redis发布订阅测试工具")
    print("=" * 60)
    
    tester = RedisPubSubTester()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, lambda s, f: signal_handler(s, f, tester))
    
    # 测试连接
    if not tester.test_connection():
        return
    
    print("\n选择监听模式:")
    print("1. 监听所有交易事件")
    print("2. 监听特定代币交易事件")
    
    try:
        choice = input("请选择 (1 或 2): ").strip()
        
        if choice == '1':
            tester.subscribe_to_all_trades()
        elif choice == '2':
            mint_address = input("请输入代币地址: ").strip()
            if mint_address:
                tester.subscribe_to_specific_mint(mint_address)
            else:
                print("❌ 无效的代币地址")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        tester.stop()
    except Exception as e:
        print(f"❌ 运行错误: {e}")

if __name__ == "__main__":
    main()