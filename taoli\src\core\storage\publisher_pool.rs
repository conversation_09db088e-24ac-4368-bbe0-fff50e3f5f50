/// 发布器集群模块
/// 
/// 负责高性能发布交易数据到Redis频道

use std::sync::Arc;
use std::sync::atomic::Ordering;
use std::thread::{self, Jo<PERSON><PERSON><PERSON><PERSON>};
use std::time::Instant;

use crossbeam_channel::Receiver;
use log::{info, error};

use crate::types::{ParsedEvent, RealTimeMetrics, PipelineConfig};
use crate::core::types::get_config;

/// 极速发布器集群
pub struct PublisherPool {
    pub workers: Vec<JoinHandle<()>>,
}

impl PublisherPool {
    pub fn new(
        receiver: Receiver<ParsedEvent>,
        config: PipelineConfig,
        metrics: Arc<RealTimeMetrics>,
    ) -> Self {
        let mut workers = Vec::new();
        // 使用配置中的Redis URL
        let redis_url = &get_config().redis.url;
        
        info!("📡 启动 {} 个发布器工作线程...", config.publisher_threads);
        
        // 创建一个原子计数器来跟踪已连接的工作线程
        let connected_count = Arc::new(std::sync::atomic::AtomicUsize::new(0));
        let total_threads = config.publisher_threads;
        
        for worker_id in 0..config.publisher_threads {
            let rx = receiver.clone();
            let metrics = Arc::clone(&metrics);
            let channel = config.redis_channel.clone();
            let redis_url = redis_url.clone();
            let connected_count = Arc::clone(&connected_count);
            
            let worker = thread::spawn(move || {
                let rt = tokio::runtime::Runtime::new().unwrap();
                
                rt.block_on(async {
                    // 启动带重试的Redis发布器
                    if let Err(e) = Self::run_publisher_with_retry(
                        worker_id,
                        &redis_url,
                        &channel,
                        &rx,
                        &metrics,
                        &connected_count,
                        total_threads,
                    ).await {
                        error!("发布器工作线程 {} 最终失败: {}", worker_id, e);
                    }
                });
            });
            
            workers.push(worker);
        }
        
        Self { workers }
    }
    
    /// 运行发布器，带自动重试机制
    async fn run_publisher_with_retry(
        worker_id: usize,
        redis_url: &str,
        channel: &str,
        receiver: &Receiver<ParsedEvent>,
        metrics: &RealTimeMetrics,
        connected_count: &std::sync::atomic::AtomicUsize,
        total_threads: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut retry_count = 0;
        let max_retries = 10;
        let retry_delay = std::time::Duration::from_secs(2);
        
        loop {
            // 只在第一次连接和失败重试时打印
            if retry_count > 0 {
                info!("📡 发布器工作线程 {} 尝试连接Redis (重试 {}/{})", worker_id, retry_count, max_retries);
            }
            
            match Self::run_publisher(worker_id, redis_url, channel, receiver, metrics, connected_count, total_threads).await {
                Ok(_) => {
                    info!("✅ 发布器工作线程 {} 正常退出", worker_id);
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    error!("❌ 发布器工作线程 {} 连接失败: {} (重试 {}/{})", 
                        worker_id, e, retry_count, max_retries);
                    
                    if retry_count >= max_retries {
                        error!("🚨 发布器工作线程 {} 达到最大重试次数", worker_id);
                        return Err(e);
                    }
                    
                    tokio::time::sleep(retry_delay).await;
                }
            }
        }
        
        Ok(())
    }
    
    /// 运行单个发布器
    async fn run_publisher(
        worker_id: usize,
        redis_url: &str,
        channel: &str,
        receiver: &Receiver<ParsedEvent>,
        metrics: &RealTimeMetrics,
        connected_count: &std::sync::atomic::AtomicUsize,
        total_threads: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 创建Redis连接
        let client = redis::Client::open(redis_url.to_string())?;
        let mut conn = client.get_multiplexed_async_connection().await?;
        
        // 增加连接计数并检查是否所有工作线程都已连接
        let current_count = connected_count.fetch_add(1, Ordering::SeqCst) + 1;
        if current_count == total_threads {
            info!("✅ 所有发布器工作线程已连接到Redis");
        }
        
        loop {
            match receiver.recv() {
                Ok(event) => {
                    let start = Instant::now();
                    
                    // 立即发布 - 无等待
                    match redis::cmd("PUBLISH")
                        .arg(channel)
                        .arg(event.json.as_str())
                        .query_async::<redis::aio::MultiplexedConnection, i64>(&mut conn)
                        .await
                    {
                        Ok(_) => {
                            let duration = start.elapsed().as_nanos() as u64;
                            metrics.publish_time_total.fetch_add(duration, Ordering::Relaxed);
                            metrics.published_count.fetch_add(1, Ordering::Relaxed);
                            metrics.publish_queue_depth.fetch_sub(1, Ordering::Relaxed);
                        }
                        Err(e) => {
                            metrics.publish_errors.fetch_add(1, Ordering::Relaxed);
                            error!("Redis发布失败: {}", e);
                            
                            // Redis错误时退出，让上层重试
                            return Err(e.into());
                        }
                    }
                }
                Err(_) => {
                    info!("发布器工作线程 {} 接收通道关闭", worker_id);
                    break;
                }
            }
        }
        
        Ok(())
    }
}