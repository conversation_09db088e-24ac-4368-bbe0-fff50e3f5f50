# 交易复刻机器人

这是一个手动选择目标Token和交易次数的交易复刻机器人。

## 功能特点

- 🎯 **手动选择目标Token**: 启动时可以输入多个Token地址和对应的交易次数
- 💰 **固定交易金额**: 每次交易固定使用0.001 SOL
- 📊 **百分百复刻**: 复刻原始交易的所有参数（池状态、金库地址等）
- 🔄 **自动计数**: 每完成一次交易自动减少剩余次数
- ✅ **完成检测**: 所有目标Token交易完成后自动停止

## 使用方法

1. **配置私钥**: 在 `src/main.rs` 第273行填入你的私钥（Base58格式）

2. **运行程序**:
   ```bash
   cargo run
   ```

3. **设置目标Token**:
   程序启动后会提示你输入：
   - Token地址（会验证格式）
   - 交易次数
   - 输入 `done` 完成设置

4. **自动监听和交易**:
   - 程序会监听Redis频道获取交易数据
   - 只有当检测到你设定的目标Token交易时才会复刻
   - 每次交易完成后会减少剩余次数
   - 所有交易完成后程序自动结束

## 示例使用流程

```
🤖 交易复刻机器人启动!
💰 固定交易金额: 0.001 SOL
🎯 设置目标Token和交易次数
💰 固定交易金额: 0.001 SOL
📝 请输入Token地址 (输入'done'完成设置): Eouhxf9NNekHvYRKkUikF5TTobziwmGH3p7X2ALGbonk
🔢 请输入交易次数: 3
✅ 已添加 Eouhxf9NNekHvYRKkUikF5TTobziwmGH3p7X2ALGbonk - 3 次交易
📝 请输入Token地址 (输入'done'完成设置): done
📊 目标Token列表:
   🎯 Eouhxf9NNekHvYRKkUikF5TTobziwmGH3p7X2ALGbonk - 3 次
🚀 开始监听Redis频道: trading_data
💰 钱包地址: [你的钱包地址]
💰 当前钱包余额: 0.1 SOL
```

## 配置参数

- **Redis频道**: `trading_data`
- **交易金额**: 0.001 SOL (固定)
- **优先费用**: 100,000 微lamports
- **计算单元限制**: 600,000

## 注意事项

- 需要足够的SOL余额（交易金额 + 手续费）
- 确保Redis服务正在运行
- 程序会完全复刻原始交易的所有参数
- 只有匹配目标Token的交易才会被复刻