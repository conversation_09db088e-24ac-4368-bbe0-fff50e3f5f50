use serde::{Deserialize, Serialize};
use std::{env, fs, path::Path};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("环境变量错误: {0}")]
    EnvVarError(String),
    
    #[error("配置验证错误: {0}")]
    ValidationError(String),
    
    #[error("文件读取错误: {0}")]
    FileError(String),
    
    #[error("配置解析错误: {0}")]
    ParseError(String),
    
    #[error("其他错误: {0}")]
    Other(String),
}

/// gRPC配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct GrpcConfig {
    /// gRPC端点
    pub endpoint: String,
    /// 连接超时时间(秒)
    pub timeout: u64,
    /// 重连间隔(秒)
    pub reconnect_interval: u64,
}

/// 程序配置
#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct ProgramsConfig {
    /// Raydium Launchpad程序ID
    pub raydium_launchpad: String,
}

/// 日志配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 是否输出到文件
    pub file_output: bool,
    /// 日志文件路径
    pub file_path: String,
}

/// 输出格式配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct OutputConfig {
    /// 是否美化JSON输出
    pub pretty_json: bool,
    /// 是否显示时间戳
    pub show_timestamp: bool,
}

/// 应用配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Config {
    /// gRPC配置
    pub grpc: GrpcConfig,
    /// 程序配置
    pub programs: ProgramsConfig,
    /// 日志配置
    pub logging: LoggingConfig,
    /// 输出格式配置
    pub output: OutputConfig,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            grpc: GrpcConfig {
                endpoint: "https://api.devnet.solana.com".to_string(),
                timeout: 30,
                reconnect_interval: 5,
            },
            programs: ProgramsConfig {
                raydium_launchpad: "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj".to_string(),
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_output: false,
                file_path: "logs/cpi-parser.log".to_string(),
            },
            output: OutputConfig {
                pretty_json: true,
                show_timestamp: true,
            },
        }
    }
}

impl Config {
    /// 从配置文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self, ConfigError> {
        let content = fs::read_to_string(path)
            .map_err(|e| ConfigError::FileError(format!("读取配置文件失败: {}", e)))?;
            
        let config: Config = toml::from_str(&content)
            .map_err(|e| ConfigError::ParseError(format!("解析配置文件失败: {}", e)))?;
            
        Ok(config)
    }
    
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self, ConfigError> {
        // 优先尝试加载配置文件
        if let Ok(config) = Self::from_file("config.toml") {
            return Ok(config);
        }
        
        // 配置文件加载失败，尝试从环境变量加载
        let grpc_endpoint = env::var("GRPC_ENDPOINT").unwrap_or_else(|_| {
            "https://api.devnet.solana.com".to_string()
        });
        
        let raydium_launchpad_program_id = env::var("RAYDIUM_LAUNCHPAD_PROGRAM_ID").unwrap_or_else(|_| {
            "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj".to_string()
        });
        
        let log_level = env::var("LOG_LEVEL").unwrap_or_else(|_| {
            "info".to_string()
        });
        
        // 从环境变量构造配置
        let config = Self {
            grpc: GrpcConfig {
                endpoint: grpc_endpoint,
                timeout: env::var("GRPC_TIMEOUT")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(30),
                reconnect_interval: env::var("GRPC_RECONNECT_INTERVAL")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(5),
            },
            programs: ProgramsConfig {
                raydium_launchpad: raydium_launchpad_program_id,
            },
            logging: LoggingConfig {
                level: log_level,
                file_output: env::var("LOG_FILE_OUTPUT")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(false),
                file_path: env::var("LOG_FILE_PATH").unwrap_or_else(|_| "logs/cpi-parser.log".to_string()),
            },
            output: OutputConfig {
                pretty_json: env::var("OUTPUT_PRETTY_JSON")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(true),
                show_timestamp: env::var("OUTPUT_SHOW_TIMESTAMP")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(true),
            },
        };
        
        Ok(config)
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<(), ConfigError> {
        if self.grpc.endpoint.is_empty() {
            return Err(ConfigError::ValidationError("gRPC 端点不能为空".to_string()));
        }
        
        if self.programs.raydium_launchpad.is_empty() {
            return Err(ConfigError::ValidationError("Raydium Launchpad 程序 ID 不能为空".to_string()));
        }
        
        Ok(())
    }
    
    /// 获取gRPC端点
    pub fn grpc_endpoint(&self) -> &str {
        &self.grpc.endpoint
    }
    
    /// 获取Raydium Launchpad程序ID
    pub fn raydium_launchpad_program_id(&self) -> &str {
        &self.programs.raydium_launchpad
    }
    
    /// 获取日志级别
    pub fn log_level(&self) -> &str {
        &self.logging.level
    }
} 