use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::{get, post, put},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use tokio::net::TcpListener;
use tower_http::cors::CorsLayer;
use tracing::{info, warn};

// 全局配置 - 所有新注册的镜像都使用这个配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalConfig {
    pub fee_addresses: Vec<String>, // 多个手续费地址池
    pub fee_rate: f64,             // 手续费比例
    pub max_daily_hours: u64,      // 每日最大运行小时
}

// 镜像信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MirrorInfo {
    pub mirror_id: String,
    pub device_id: String,
    pub device_name: String,
    pub version: String,
    pub registered_at: chrono::DateTime<chrono::Utc>,
    pub status: String,
    pub config: GlobalConfig, // 注册时的配置快照
}

// 注册请求
#[derive(Debug, Deserialize)]
pub struct RegisterRequest {
    pub device_id: String,
    pub device_name: String,
    pub version: String,
}

// 注册响应
#[derive(Debug, Serialize)]
pub struct RegisterResponse {
    pub code: u32,
    pub message: String,
    pub mirror_id: String,
    pub config: GlobalConfig,
}

// 配置更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateConfigRequest {
    pub fee_addresses: Vec<String>,
    pub fee_rate: f64,
    pub max_daily_hours: u64,
}

// 通用响应
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub code: u32,
    pub message: String,
    pub data: Option<T>,
}

// 应用状态
#[derive(Debug)]
pub struct AppState {
    pub global_config: Arc<RwLock<GlobalConfig>>,
    pub mirrors: Arc<RwLock<HashMap<String, MirrorInfo>>>,
    pub next_id: Arc<RwLock<u32>>,
}

impl AppState {
    pub fn new() -> Self {
        // 默认配置
        let default_config = GlobalConfig {
            fee_addresses: vec!["Cd4magyg5n2Qs1dLZEoLfCRYKxHrnkf61RBr14PyVbHW".to_string()],
            fee_rate: 0.35,
            max_daily_hours: 8,
        };

        Self {
            global_config: Arc::new(RwLock::new(default_config)),
            mirrors: Arc::new(RwLock::new(HashMap::new())),
            next_id: Arc::new(RwLock::new(1)),
        }
    }
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    info!("启动镜像管理端后端服务...");
    
    // 创建应用状态
    let state = Arc::new(AppState::new());
    
    // 创建路由
    let app = Router::new()
        // 镜像端接口
        .route("/api/mirror/register", post(register_mirror))
        .route("/api/mirrors", get(list_mirrors))
        // 管理端接口  
        .route("/api/admin/config", get(get_global_config))
        .route("/api/admin/config", put(update_global_config))
        .route("/api/admin/mirrors", get(list_all_mirrors))
        .layer(CorsLayer::permissive())
        .with_state(state);
    
    // 启动服务器
    let listener = TcpListener::bind("0.0.0.0:8081").await?;
    info!("管理端后端服务已启动，监听端口: 8081");
    
    axum::serve(listener, app).await?;
    
    Ok(())
}

// 镜像端注册接口
async fn register_mirror(
    State(state): State<Arc<AppState>>,
    Json(req): Json<RegisterRequest>,
) -> Result<Json<RegisterResponse>, StatusCode> {
    info!("收到镜像端注册请求: 设备ID={}, 名称={}", req.device_id, req.device_name);
    
    // 检查设备是否已注册
    {
        let mirrors = state.mirrors.read().unwrap();
        if let Some(existing) = mirrors.values().find(|m| m.device_id == req.device_id) {
            warn!("设备已注册，返回现有配置: {}", req.device_id);
            return Ok(Json(RegisterResponse {
                code: 200,
                message: "设备已注册".to_string(),
                mirror_id: existing.mirror_id.clone(),
                config: existing.config.clone(),
            }));
        }
    }
    
    // 生成新的镜像ID
    let mirror_id = {
        let mut next_id = state.next_id.write().unwrap();
        let id = format!("M{:03}", *next_id);
        *next_id += 1;
        id
    };
    
    // 获取当前全局配置
    let current_config = {
        let config = state.global_config.read().unwrap();
        config.clone()
    };
    
    // 创建镜像信息
    let mirror_info = MirrorInfo {
        mirror_id: mirror_id.clone(),
        device_id: req.device_id,
        device_name: req.device_name,
        version: req.version,
        registered_at: chrono::Utc::now(),
        status: "已注册".to_string(),
        config: current_config.clone(),
    };
    
    // 存储镜像信息
    {
        let mut mirrors = state.mirrors.write().unwrap();
        mirrors.insert(mirror_id.clone(), mirror_info);
    }
    
    info!("镜像端注册成功: ID={}", mirror_id);
    
    Ok(Json(RegisterResponse {
        code: 200,
        message: "注册成功".to_string(),
        mirror_id,
        config: current_config,
    }))
}

// 获取镜像端列表（简化版，给镜像端用）
async fn list_mirrors(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<String>>>, StatusCode> {
    let mirrors = state.mirrors.read().unwrap();
    let mirror_ids: Vec<String> = mirrors.keys().cloned().collect();
    
    Ok(Json(ApiResponse {
        code: 200,
        message: "获取成功".to_string(),
        data: Some(mirror_ids),
    }))
}

// 管理端 - 获取全局配置
async fn get_global_config(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<GlobalConfig>>, StatusCode> {
    let config = state.global_config.read().unwrap();
    
    Ok(Json(ApiResponse {
        code: 200,
        message: "获取配置成功".to_string(),
        data: Some(config.clone()),
    }))
}

// 管理端 - 更新全局配置
async fn update_global_config(
    State(state): State<Arc<AppState>>,
    Json(req): Json<UpdateConfigRequest>,
) -> Result<Json<ApiResponse<GlobalConfig>>, StatusCode> {
    info!("更新全局配置: 地址数量={}, 费率={}", req.fee_addresses.len(), req.fee_rate);
    
    // 验证配置
    if req.fee_addresses.is_empty() {
        return Err(StatusCode::BAD_REQUEST);
    }
    
    if req.fee_rate < 0.0 || req.fee_rate > 100.0 {
        return Err(StatusCode::BAD_REQUEST);
    }
    
    // 更新配置
    let new_config = GlobalConfig {
        fee_addresses: req.fee_addresses,
        fee_rate: req.fee_rate,
        max_daily_hours: req.max_daily_hours,
    };
    
    {
        let mut config = state.global_config.write().unwrap();
        *config = new_config.clone();
    }
    
    info!("全局配置更新成功");
    
    Ok(Json(ApiResponse {
        code: 200,
        message: "配置更新成功".to_string(),
        data: Some(new_config),
    }))
}

// 管理端 - 获取所有镜像端详细信息
async fn list_all_mirrors(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<MirrorInfo>>>, StatusCode> {
    let mirrors = state.mirrors.read().unwrap();
    let mirror_list: Vec<MirrorInfo> = mirrors.values().cloned().collect();
    
    info!("返回镜像端列表，共 {} 个", mirror_list.len());
    
    Ok(Json(ApiResponse {
        code: 200,
        message: "获取镜像端列表成功".to_string(),
        data: Some(mirror_list),
    }))
} 