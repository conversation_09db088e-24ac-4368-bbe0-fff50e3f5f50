import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../components/Layout/MainLayout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false 
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { 
          title: '仪表盘',
          icon: 'Odometer'
        }
      },
      {
        path: 'mirrors',
        name: 'Mirrors',
        component: () => import('../views/Mirrors/Index.vue'),
        meta: { 
          title: '镜像管理',
          icon: 'Monitor'
        }
      },
      {
        path: 'config',
        name: 'Config',
        component: () => import('../views/Config/Index.vue'),
        meta: { 
          title: '配置管理',
          icon: 'Setting'
        }
      },
      {
        path: 'monitoring',
        name: 'Monitoring',
        component: () => import('../views/Monitoring/Index.vue'),
        meta: { 
          title: '数据监控',
          icon: 'Monitor'
        }
      },
      {
        path: 'tickets',
        name: 'Tickets',
        component: () => import('../views/Tickets/Index.vue'),
        meta: { 
          title: '工单管理',
          icon: 'Tickets'
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('admin_token')
  
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/')
  } else {
    next()
  }
})

export default router 