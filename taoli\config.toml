# PumpFun 零延迟解析器配置文件

# gRPC连接配置
[grpc]
# gRPC端点 - 使用参考资料中的工作端点
endpoint = "https://solana-yellowstone-grpc.publicnode.com:443"
# 连接超时时间(秒)
timeout = 30
# 重连间隔(秒)
reconnect_interval = 5

# Redis配置
[redis]
# Redis连接URL
url = "redis://127.0.0.1/"
# 发布通道
channel = "trades_channel"
# 连接池大小
pool_size = 500

# 处理器配置
[processor]
# 解析线程数(设置为8个线程)
parser_threads = 16
# 发布线程数(设置为4个线程)
publisher_threads = 16
# gRPC缓冲区大小
grpc_buffer_size = 100000
# 队列容量
queue_capacity = 1000000
# 批处理大小
batch_size = 1000

# 程序配置
[programs]
# PumpFun程序ID - 使用参考资料中的正确ID
pump_program = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
# Bonk程序ID
bonk_program = "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj"

# 日志配置
[logging]
# 日志级别: debug, info, warn, error
level = "info"
# 是否输出到文件
file_output = false
# 日志文件路径
file_path = "logs/parser.log" 