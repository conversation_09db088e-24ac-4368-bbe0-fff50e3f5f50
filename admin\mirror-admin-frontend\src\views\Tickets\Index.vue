<template>
  <div class="tickets-page">
    <div class="page-title">
      <h2>工单管理</h2>
      <p>处理用户提交的技术支持工单</p>
    </div>

    <!-- 筛选区域 -->
    <div class="content-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="工单状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="filterForm.priority" placeholder="选择优先级" style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="提交时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="success" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 工单列表 -->
    <div class="content-card">
      <div class="card-header">
        <h3 class="card-title">工单列表</h3>
        <div>
          <el-badge :value="pendingCount" type="danger">
            <el-button type="warning">待处理工单</el-button>
          </el-badge>
        </div>
      </div>

      <el-table :data="filteredTickets" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="工单号" width="100" />
        
        <el-table-column prop="title" label="标题" min-width="200" />
        
        <el-table-column prop="mirrorId" label="镜像ID" width="120" />
        
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="submittedAt" label="提交时间" width="150" />
        
        <el-table-column prop="lastReply" label="最后回复" width="150" />
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewTicket(row)">查看</el-button>
            <el-button size="small" type="primary" @click="replyTicket(row)">
              回复
            </el-button>
            <el-dropdown @command="(cmd) => handleTicketAction(cmd, row)">
              <el-button size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="resolve">标记已解决</el-dropdown-item>
                  <el-dropdown-item command="close">关闭工单</el-dropdown-item>
                  <el-dropdown-item command="assign">分配处理人</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>

    <!-- 工单详情对话框 -->
    <el-dialog v-model="showTicketDetail" title="工单详情" width="800px">
      <div v-if="currentTicket">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单号">{{ currentTicket.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentTicket.status)">
              {{ getStatusText(currentTicket.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标题" :span="2">{{ currentTicket.title }}</el-descriptions-item>
          <el-descriptions-item label="镜像ID">{{ currentTicket.mirrorId }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(currentTicket.priority)">
              {{ getPriorityText(currentTicket.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ currentTicket.submittedAt }}</el-descriptions-item>
          <el-descriptions-item label="最后更新">{{ currentTicket.lastReply }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin: 20px 0;">
          <h4>问题描述</h4>
          <div style="background: #f5f5f5; padding: 12px; border-radius: 4px; margin-top: 8px;">
            {{ currentTicket.description }}
          </div>
        </div>
        
        <div style="margin: 20px 0;">
          <h4>回复记录</h4>
          <div class="reply-list">
            <div v-for="reply in currentTicket.replies" :key="reply.id" class="reply-item">
              <div class="reply-header">
                <span class="reply-author">{{ reply.author }}</span>
                <span class="reply-time">{{ reply.time }}</span>
              </div>
              <div class="reply-content">{{ reply.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 回复工单对话框 -->
    <el-dialog v-model="showReplyDialog" title="回复工单" width="600px">
      <el-form :model="replyForm" label-width="80px">
        <el-form-item label="回复内容" required>
          <el-input
            v-model="replyForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入回复内容..."
          />
        </el-form-item>
        
        <el-form-item label="状态变更">
          <el-select v-model="replyForm.newStatus" placeholder="选择新状态">
            <el-option label="保持当前状态" value="" />
            <el-option label="处理中" value="processing" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showReplyDialog = false">取消</el-button>
        <el-button type="primary" @click="submitReply">发送回复</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const showTicketDetail = ref(false)
const showReplyDialog = ref(false)
const currentTicket = ref(null)

// 筛选表单
const filterForm = reactive({
  status: '',
  priority: '',
  dateRange: []
})

// 回复表单
const replyForm = reactive({
  content: '',
  newStatus: ''
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 工单数据
const tickets = ref([])

// 过滤后的工单
const filteredTickets = computed(() => {
  let filtered = tickets.value
  
  if (filterForm.status) {
    filtered = filtered.filter(t => t.status === filterForm.status)
  }
  
  if (filterForm.priority) {
    filtered = filtered.filter(t => t.priority === filterForm.priority)
  }
  
  pagination.total = filtered.length
  return filtered
})

// 待处理工单数
const pendingCount = computed(() => {
  return tickets.value.filter(t => t.status === 'pending').length
})

// 获取分类文本
const getCategoryText = (category) => {
  const texts = {
    technical: '技术问题',
    config: '配置问题',
    feature: '功能建议',
    bug: 'Bug报告'
  }
  return texts[category] || category
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const types = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return types[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return texts[status] || status
}

// 筛选处理
const handleFilter = () => {
  pagination.current = 1
  ElMessage.success('筛选完成')
}

// 重置筛选
const resetFilter = () => {
  filterForm.status = ''
  filterForm.priority = ''
  filterForm.dateRange = []
  pagination.current = 1
  ElMessage.info('已重置筛选条件')
}

// 刷新数据
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    ElMessage.success('数据已刷新')
    loading.value = false
  }, 1000)
}

// 查看工单
const viewTicket = (ticket) => {
  currentTicket.value = ticket
  showTicketDetail.value = true
}

// 回复工单
const replyTicket = (ticket) => {
  currentTicket.value = ticket
  replyForm.content = ''
  replyForm.newStatus = ''
  showReplyDialog.value = true
}

// 提交回复
const submitReply = () => {
  if (!replyForm.content.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }
  
  ElMessage.success('回复已发送')
  showReplyDialog.value = false
}

// 工单操作
const handleTicketAction = async (command, ticket) => {
  try {
    switch (command) {
      case 'resolve':
        await ElMessageBox.confirm('确定要标记此工单为已解决吗？', '确认操作')
        ElMessage.success('工单已标记为已解决')
        break
      case 'close':
        await ElMessageBox.confirm('确定要关闭此工单吗？', '确认操作')
        ElMessage.success('工单已关闭')
        break
      case 'assign':
        ElMessage.info('分配处理人功能开发中')
        break
    }
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  // TODO: 加载工单数据
})
</script>

<style scoped>
.tickets-page {
  padding: 0;
}

.page-title {
  margin-bottom: 24px;
}

.page-title h2 {
  font-size: 20px;
  color: #333;
  margin: 0 0 4px 0;
}

.page-title p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.reply-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
}

.reply-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.reply-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.reply-author {
  font-weight: 600;
  color: #333;
}

.reply-time {
  color: #666;
  font-size: 12px;
}

.reply-content {
  color: #555;
  line-height: 1.6;
}
</style> 