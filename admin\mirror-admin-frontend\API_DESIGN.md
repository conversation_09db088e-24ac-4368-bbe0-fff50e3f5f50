# 镜像管理系统 API 设计文档

## 🏗️ 系统架构

```
镜像端 (用户设备) ←→ 管理端后台 (管理服务器) ←→ 管理前端 (Web界面)
```

## 📡 镜像端注册与通信

### 1. 镜像端注册 (Mirror Registration)

**接口**: `POST /api/mirror/register`

**请求地址**: 
- 开发环境: `http://*************:8080/api/mirror/register`
- 生产环境: `http://your-domain.com/api/mirror/register`

**请求头**:
```http
Content-Type: application/json
```

**请求体**:
```json
{
  "device_id": "unique-device-fingerprint",
  "device_name": "用户端-001",
  "version": "v1.2.3",
  "capabilities": [
    "auto_trade",
    "copy_trade",
    "fee_collection"
  ],
  "system_info": {
    "os": "Windows 10",
    "arch": "x64",
    "memory": "16GB",
    "cpu": "Intel i7"
  },
  "network_info": {
    "ip": "*************",
    "mac": "AA:BB:CC:DD:EE:FF"
  }
}
```

**响应体**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "mirror_id": "M001",
    "auth_token": "jwt-token-string",
    "websocket_url": "ws://*************:8080/ws/mirror/M001",
    "config": {
      "heartbeat_interval": 30,
      "config_refresh_interval": 300,
      "upload_interval": 60
    },
    "permissions": {
      "auto_trade": true,
      "max_daily_hours": 8,
      "allowed_time_range": ["09:00", "18:00"]
    }
  }
}
```

### 2. 心跳保持 (Heartbeat)

**接口**: `POST /api/mirror/heartbeat/{mirror_id}`

**频率**: 每30秒一次

**请求体**:
```json
{
  "status": "online",
  "running_time": 7200,
  "last_transaction": "2024-01-15T14:30:25Z",
  "performance": {
    "cpu_usage": 15.5,
    "memory_usage": 2048,
    "network_latency": 45
  }
}
```

### 3. 配置拉取 (Config Pull)

**接口**: `GET /api/mirror/config/{mirror_id}`

**频率**: 每5分钟一次

**响应体**:
```json
{
  "code": 200,
  "data": {
    "fee_config": {
      "current_address": "0x1234...5678",
      "rate": 2.5,
      "next_rotation": "2024-01-15T15:00:00Z"
    },
    "time_limits": {
      "daily_hours_remaining": 5.5,
      "force_stop_at": "2024-01-15T18:00:00Z"
    },
    "trading_config": {
      "max_gas_price": 50,
      "slippage_tolerance": 0.5
    }
  }
}
```

### 4. 数据上传 (Data Upload)

**接口**: `POST /api/mirror/upload/{mirror_id}`

**频率**: 每分钟一次（异步后台）

**请求体**:
```json
{
  "transactions": [
    {
      "tx_hash": "0xabcd...1234",
      "timestamp": "2024-01-15T14:30:25Z",
      "amount": "1.5",
      "fee": "0.0375",
      "status": "success",
      "gas_used": 21000
    }
  ],
  "runtime_stats": {
    "total_transactions": 150,
    "success_rate": 98.5,
    "total_fees_collected": "3.75",
    "avg_response_time": 45
  }
}
```

### 5. WebSocket 实时通信

**连接地址**: `ws://*************:8080/ws/mirror/{mirror_id}?token={auth_token}`

**管理端 → 镜像端 消息**:
```json
{
  "type": "command",
  "data": {
    "action": "stop",
    "reason": "time_limit_reached"
  }
}

{
  "type": "config_update",
  "data": {
    "fee_address": "0x5678...9abc",
    "rate": 3.0
  }
}
```

**镜像端 → 管理端 消息**:
```json
{
  "type": "status_update",
  "data": {
    "status": "running",
    "current_task": "executing_trade"
  }
}
```

## 🔐 安全机制

### 1. 设备指纹验证
- 基于硬件信息生成唯一设备ID
- 防止同一凭证在不同设备使用

### 2. JWT Token 认证
- 注册后获得JWT Token
- 所有API请求都需要Bearer认证

### 3. WebSocket 加密
- 使用TLS加密WebSocket连接
- 消息内容AES加密

### 4. 时间限制强制执行
- 心跳断开1分钟自动停止服务
- 重启无法绕过时间限制

## 🌍 网络配置

### 开发环境
- 前端: http://localhost:3000
- 后端: http://localhost:8080
- 局域网访问: http://*************:3000

### 镜像端应该连接的地址
- **局域网内**: `http://*************:8080`
- **外网**: 需要配置端口转发或使用公网IP

### 端口映射建议
```bash
# 路由器端口转发配置
外网端口 8080 → 内网 *************:8080
外网端口 3000 → 内网 *************:3000
```

## 📋 错误码定义

| 错误码 | 说明 |
|--------|------|
| 1001 | 设备已注册 |
| 1002 | 设备被禁用 |
| 1003 | 超出时间限制 |
| 1004 | 心跳超时 |
| 1005 | 配置同步失败 |
| 1006 | 权限不足 |

## 🔄 注册流程示例

```javascript
// 镜像端注册代码示例
const registerMirror = async () => {
  const deviceInfo = {
    device_id: generateDeviceFingerprint(),
    device_name: "用户端-001",
    version: "v1.2.3",
    // ... 其他信息
  };
  
  const response = await fetch('http://*************:8080/api/mirror/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(deviceInfo)
  });
  
  const result = await response.json();
  if (result.code === 200) {
    // 保存认证信息
    localStorage.setItem('mirror_token', result.data.auth_token);
    localStorage.setItem('mirror_id', result.data.mirror_id);
    
    // 建立WebSocket连接
    connectWebSocket(result.data.websocket_url, result.data.auth_token);
    
    // 启动心跳
    startHeartbeat(result.data.mirror_id);
  }
};
``` 