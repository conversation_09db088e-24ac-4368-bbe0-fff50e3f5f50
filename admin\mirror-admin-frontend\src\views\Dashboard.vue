<template>
  <div class="dashboard">
    <div class="page-title">
      <h2>仪表盘</h2>
      <p>系统概览和运行状态</p>
    </div>

    <!-- 统计数据卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon" style="background: #e6f7ff; color: #1890ff;">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">总镜像数</div>
          <div class="stats-value">{{ stats.totalMirrors }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon" style="background: #f6ffed; color: #52c41a;">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">在线镜像</div>
          <div class="stats-value">{{ stats.onlineMirrors }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon" style="background: #fff2e8; color: #faad14;">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">警告状态</div>
          <div class="stats-value">{{ stats.warningMirrors }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon" style="background: #fff1f0; color: #ff4d4f;">
          <el-icon><CircleClose /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">离线镜像</div>
          <div class="stats-value">{{ stats.offlineMirrors }}</div>
        </div>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 实时状态 -->
      <el-col :span="16">
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">镜像端实时状态</h3>
            <el-button size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <el-table :data="recentMirrors" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="设备ID" width="120" />
            <el-table-column prop="name" label="设备名称" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="getStatusType(row.status)"
                  size="small"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastHeartbeat" label="最后心跳" width="150" />
            <el-table-column prop="runningTime" label="运行时长" width="120" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button 
                  size="small" 
                  :type="row.status === 'online' ? 'danger' : 'primary'"
                  @click="toggleMirrorStatus(row)"
                >
                  {{ row.status === 'online' ? '停止' : '启动' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>

      <!-- 系统信息 -->
      <el-col :span="8">
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">系统信息</h3>
          </div>
          
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">服务器状态</span>
              <el-tag type="success" size="small">正常运行</el-tag>
            </div>
            
            <div class="info-item">
              <span class="info-label">系统版本</span>
              <span class="info-value">v1.0.0</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">运行时间</span>
              <span class="info-value">3天 12小时</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">今日交易</span>
              <span class="info-value">1,234 笔</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">数据库状态</span>
              <el-tag type="success" size="small">连接正常</el-tag>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">快捷操作</h3>
          </div>
          
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/mirrors')">
              <el-icon><Monitor /></el-icon>
              管理镜像
            </el-button>
            
            <el-button type="success" @click="$router.push('/config')">
              <el-icon><Setting /></el-icon>
              系统配置
            </el-button>
            
            <el-button type="warning" @click="$router.push('/tickets')">
              <el-icon><Tickets /></el-icon>
              查看工单
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

// 统计数据
const stats = reactive({
  totalMirrors: 0,
  onlineMirrors: 0,
  warningMirrors: 0,
  offlineMirrors: 0
})

// 镜像端数据
const recentMirrors = ref([])

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    online: 'success',
    offline: 'danger',
    warning: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    online: '在线',
    offline: '离线',
    warning: '警告'
  }
  return texts[status] || '未知'
}

// 切换镜像状态
const toggleMirrorStatus = (mirror) => {
  const action = mirror.status === 'online' ? '停止' : '启动'
  ElMessage.info(`${action}镜像 ${mirror.name} 功能开发中`)
}

// 刷新数据
const refreshData = () => {
  loading.value = true
  
  // 模拟刷新数据
  setTimeout(() => {
    ElMessage.success('数据已刷新')
    loading.value = false
  }, 1000)
}

// 页面加载时获取数据
onMounted(() => {
  // TODO: 调用API获取实际数据
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-title {
  margin-bottom: 24px;
}

.page-title h2 {
  font-size: 20px;
  color: #333;
  margin: 0 0 4px 0;
}

.page-title p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.system-info {
  padding: 0;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-actions .el-button {
  justify-content: flex-start;
  text-align: left;
}
</style> 