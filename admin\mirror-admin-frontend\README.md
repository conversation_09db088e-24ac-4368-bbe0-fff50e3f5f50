# 镜像管理后台

基于Vue 3 + Element Plus的现代化管理后台系统，用于管理和控制镜像端设备。

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **开发语言**: JavaScript

## 项目结构

```
src/
├── api/                    # API接口配置
│   └── index.js           # HTTP请求封装和API定义
├── assets/                # 静态资源
│   └── styles/
│       └── main.css       # 全局样式
├── components/            # 通用组件
│   └── Layout/
│       └── MainLayout.vue # 主布局组件
├── router/                # 路由配置
│   └── index.js           # 路由定义和守卫
├── stores/                # 状态管理
│   └── index.js           # Pinia状态定义
├── views/                 # 页面组件
│   ├── Dashboard.vue      # 仪表盘
│   ├── Login.vue          # 登录页面
│   ├── Config/
│   │   └── Index.vue      # 配置管理
│   ├── Mirrors/
│   │   └── Index.vue      # 镜像管理
│   ├── Monitoring/
│   │   └── Index.vue      # 数据监控
│   └── Tickets/
│       └── Index.vue      # 工单管理
├── App.vue                # 根组件
└── main.js                # 入口文件
```

## 核心功能模块

### 1. 仪表盘 (Dashboard)
- 系统概览和运行状态
- 镜像端实时状态监控
- 快捷操作入口
- 统计数据展示

### 2. 镜像管理 (Mirrors)
- 查看所有注册的镜像端设备
- 远程控制镜像启停
- 设置运行时间限制
- 批量操作功能
- 实时状态监控

### 3. 配置管理 (Config)
- 手续费地址池管理
- 手续费比例设置
- 时间限制配置
- 高级系统配置

### 4. 数据监控 (Monitoring)
- 实时交易数据展示
- 系统性能监控
- 日志查看
- 数据导出功能

### 5. 工单管理 (Tickets)
- 用户工单处理
- 工单状态管理
- 回复和跟进
- 优先级设置

## 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 开发配置

### API代理配置
开发环境下，所有`/api`请求会被代理到`http://localhost:8080`。

### 路由守卫
- 自动检查登录状态
- 未登录用户重定向到登录页
- 已登录用户访问登录页会重定向到首页

### 状态管理
使用Pinia管理以下状态：
- 用户登录状态和信息
- 镜像端数据和状态
- 系统配置信息

## API接口设计

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新Token

### 镜像管理接口
- `GET /api/mirrors` - 获取镜像列表
- `GET /api/mirrors/{id}` - 获取镜像详情
- `PUT /api/mirrors/{id}/status` - 更新镜像状态
- `PUT /api/mirrors/{id}/time-limit` - 设置时间限制

### 配置管理接口
- `GET /api/config/fee` - 获取手续费配置
- `PUT /api/config/fee` - 更新手续费配置
- `GET /api/config/time` - 获取时间配置
- `PUT /api/config/time` - 更新时间配置

### 监控接口
- `GET /api/monitoring/transactions/{mirrorId}` - 获取交易数据
- `GET /api/monitoring/stats` - 获取统计数据
- `GET /api/monitoring/realtime` - 获取实时数据

### 工单接口
- `GET /api/tickets` - 获取工单列表
- `GET /api/tickets/{id}` - 获取工单详情
- `PUT /api/tickets/{id}` - 更新工单
- `POST /api/tickets/{id}/reply` - 回复工单

## 部署说明

1. 构建项目：`npm run build`
2. 将`dist`目录部署到Web服务器
3. 配置反向代理，将API请求转发到后端服务
4. 确保支持前端路由的History模式

## 注意事项

- 所有页面组件已实现基础框架，具体功能需要对接后端API
- 登录功能使用模拟数据，需要替换为真实的认证接口
- 图表和复杂数据可视化组件需要后续集成
- 响应式设计已考虑移动端适配

## 开发规范

- 使用Vue 3 Composition API编写组件
- 遵循Element Plus设计规范
- 统一使用中文进行界面文本
- API请求统一使用axios封装
- 错误处理和用户反馈使用Element Plus消息组件
