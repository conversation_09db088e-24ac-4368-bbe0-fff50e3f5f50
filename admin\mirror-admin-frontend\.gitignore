# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

# 开发工具和缓存
.vite
.nuxt
.next
.svelte-kit

# 环境文件
.env
.env.local
.env.production
.env.development

# 编辑器和IDE
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json

# 调试文件
*.tgz
*.tar.gz

# 运行时文件
*.pid
*.seed
*.pid.lock

# npm包文件
*.tgz
package-lock.json
