<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <header class="layout-header">
      <div class="flex-center">
        <h1 style="margin: 0; font-size: 18px;">镜像管理后台</h1>
      </div>
      
      <div class="flex-center">
        <el-dropdown @command="handleUserAction">
          <span class="user-info">
            <el-icon style="margin-right: 8px;"><User /></el-icon>
            管理员
            <el-icon style="margin-left: 4px;"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <div class="layout-main">
      <!-- 侧边栏 -->
      <aside class="layout-sidebar">
        <el-menu
          :default-active="currentRoute"
          class="sidebar-menu"
          router
          :collapse="false"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Odometer /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          
          <el-menu-item index="/mirrors">
            <el-icon><Monitor /></el-icon>
            <span>镜像管理</span>
          </el-menu-item>
          
          <el-menu-item index="/config">
            <el-icon><Setting /></el-icon>
            <span>配置管理</span>
          </el-menu-item>
          
          <el-menu-item index="/monitoring">
            <el-icon><DataLine /></el-icon>
            <span>数据监控</span>
          </el-menu-item>
          
          <el-menu-item index="/tickets">
            <el-icon><Tickets /></el-icon>
            <span>工单管理</span>
          </el-menu-item>
        </el-menu>
      </aside>

      <!-- 内容区域 -->
      <main class="layout-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in">
            <component :is="Component" :key="route.path" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../../stores'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 当前路由
const currentRoute = computed(() => route.path)

// 用户操作处理
const handleUserAction = (command) => {
  switch (command) {
    case 'profile':
      // TODO: 打开个人信息弹窗
      ElMessage.info('个人信息功能开发中')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      ElMessage.success('已退出登录')
      break
  }
}
</script>

<style scoped>
.user-info {
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-right: 3px solid #1890ff;
}
</style> 