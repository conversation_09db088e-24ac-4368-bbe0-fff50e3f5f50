use crate::shared::types::{HotPathTrade, WalletConfig, FollowMode};
use serde_derive::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

/// 主筛选配置，包含一个从钱包地址到其计算配置的映射
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct FilterConfig {
    pub allowed_creators: HashSet<String>,
    pub block_list_mint: HashSet<String>,
    pub min_price: f64,
    pub max_price: f64,
    /// 存储每个受监控钱包的计算配置
    pub wallet_configs: HashMap<String, WalletConfig>,
}

/// 用于控制筛选器配置更新的命令
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterCommand {
    AddCreator(String),
    RemoveCreator(String),
    AddToBlockList(String),
    RemoveFromBlockList(String),
    SetMinPrice(f64),
    SetMaxPrice(f64),
    /// 更新或插入一个钱包的完整计算配置
    UpdateWallet(WalletConfig),
    /// 移除一个钱包的配置
    RemoveWallet(String),
}

impl FilterConfig {
    /// 根据命令应用更改，返回一个新的配置实例
    pub fn apply_command(mut self, command: FilterCommand) -> Self {
        match command {
            FilterCommand::AddCreator(creator) => {
                self.allowed_creators.insert(creator);
            }
            FilterCommand::RemoveCreator(creator) => {
                self.allowed_creators.remove(&creator);
            }
            FilterCommand::AddToBlockList(mint) => {
                self.block_list_mint.insert(mint);
            }
            FilterCommand::RemoveFromBlockList(mint) => {
                self.block_list_mint.remove(&mint);
            }
            FilterCommand::SetMinPrice(price) => {
                self.min_price = price;
            }
            FilterCommand::SetMaxPrice(price) => {
                self.max_price = price;
            }
            FilterCommand::UpdateWallet(wallet_config) => {
                // 使用 signer 地址作为 key
                self.wallet_configs.insert(wallet_config.wallet_address.clone(), wallet_config);
            }
            FilterCommand::RemoveWallet(address) => {
                self.wallet_configs.remove(&address);
            }
        }
        self
    }
}

/// Filter 结构体只负责筛选，不关心计算细节。
/// 它决定一笔交易是否应该被"放行"到下一个阶段（计算阶段）。
#[derive(Debug, Default, Clone)]
pub struct Filter {
    pub config: FilterConfig,
}

impl Filter {
    pub fn new(config: FilterConfig) -> Self {
        Self {
            config,
        }
    }

    /// 通用筛选逻辑：只检查交易是否来自一个我们正在跟踪且激活的钱包，并检查价格是否在范围内。
    /// 如果是，则返回该钱包的配置。
    pub fn get_config_if_tracked(&self, trade: &HotPathTrade) -> Option<&WalletConfig> {
        // 1. 检查交易价格是否有效
        if trade.price <= 0.0 {
            return None;
        }
        // 2. 全局价格筛选
        if self.config.min_price > 0.0 && trade.price < self.config.min_price {
            return None; // 小于全局最低价
        }
        if self.config.max_price > 0.0 && trade.price > self.config.max_price {
            return None; // 大于全局最高价
        }

        // 3. 检查签名者地址，并获取其专属配置
        let config = self.config.wallet_configs.get(&trade.signer)?;

        // 4. 检查该配置是否激活
        if !config.is_active {
            return None;
        }

        // 所有条件都满足，返回配置的引用
        Some(config)
    }

    /// 核心购买金额计算逻辑：传入已经过筛选的交易和配置，计算最终购买金额。
    /// 如果计算出的金额有效，则返回 Some(buy_amount)，否则返回 None。
    pub fn check_and_get_buy_details(&self, trade: &HotPathTrade, config: &WalletConfig) -> Option<f64> {
        // --- 个人钱包价格筛选 ---
        // 检查最低价格乘数
        if let Some(min_multiplier) = config.min_price_multiplier {
            if min_multiplier > 0.0 && trade.price < min_multiplier {
                return None; // 价格低于个人设置的最低价格
            }
        }

        // 检查最高价格乘数
        if let Some(max_multiplier) = config.max_price_multiplier {
            if max_multiplier > 0.0 && trade.price > max_multiplier {
                return None; // 价格高于个人设置的最高价格
            }
        }

        // --- 购买逻辑 ---
        // 先用sol_amount_min和sol_amount_max筛选被跟随者的交易金额
        if let Some(min_sol) = config.sol_amount_min {
            if trade.sol_cost < min_sol {
                return None; // 被跟随者交易金额小于最小值，不跟单
            }
        }
        if let Some(max_sol) = config.sol_amount_max {
            if trade.sol_cost > max_sol {
                return None; // 被跟随者交易金额大于最大值，不跟单
            }
        }

        // 根据跟单模式，计算跟单金额 (SOL)
        let buy_amount_sol = match config.follow_mode {
            FollowMode::Percentage => {
                // 百分比跟单模式
                match config.follow_percentage {
                    Some(pct) => {
                        let calculated_amount = trade.sol_cost * (pct / 100.0);
                        // 百分比模式下，可以对计算出的金额进行二次限制（可选）
                        calculated_amount
                    },
                    None => return None, // 百分比模式下必须设置跟单比例
                }
            },
            FollowMode::FixedAmount => {
                // 固定金额跟单模式：直接使用固定金额，不受被跟随者金额影响
                match config.fixed_follow_amount_sol {
                    Some(amount) => amount,
                    None => return None, // 固定金额模式下必须设置固定金额
                }
            }
        };

        // 如果最终购买金额无效，则不交易
        if buy_amount_sol <= 0.0 {
            return None;
        }

        // 所有检查通过，返回最终确定的购买金额
        Some(buy_amount_sol)
    }
} 