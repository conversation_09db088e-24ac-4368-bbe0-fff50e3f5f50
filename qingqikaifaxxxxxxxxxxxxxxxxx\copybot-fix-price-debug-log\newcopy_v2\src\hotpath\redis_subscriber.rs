use anyhow::{anyhow, Result};
use futures_util::stream::StreamExt;
use tracing::{info, debug, warn, error};
use crate::hotpath::parser::parse_trades_from_redis_bytes;
use crate::hotpath::filter::Filter;
use crate::hotpath::calculator::{Calculator, CalculateInput};
use crate::hotpath::transaction_builder::TransactionBuilder;
use crate::hotpath::protocol_detector::{detect_protocol};
use crate::services::transaction_sender::TransactionSender;
use crate::services::ata_cache::AtaCache;
use crate::shared::types::{TradeType, TradeRecord, Protocol};
use std::time::Instant;
use colored::Colorize;
use base64::{Engine as _, engine::general_purpose};
use std::sync::Arc;
use arc_swap::ArcSwap;
use chrono::Local;
use dashmap::DashMap;
use tokio::sync::mpsc;
use crate::services::transaction_tracker::TransactionTracker;
use solana_sdk::signature::Signature;
use std::str::FromStr;
use crate::services::transaction_tracker::TrackRequest;
use crate::services::price_broadcast::PriceBroadcastManager;
use uuid::Uuid;
use crate::services::senders::{flashblock_sender::FlashblockSender, oslot_sender::OslotSender};
use crate::shared::global_config::get_accelerator_config;
use solana_sdk::pubkey::Pubkey;

const TRADES_CHANNEL: &str = "trades_channel";

/// 截断地址以方便显示，格式为 "前6...后4"
fn truncate_address(address: &str) -> String {
    if address.len() > 10 {
        format!("{}...{}", &address[..6], &address[address.len()-4..])
    } else {
        address.to_string()
    }
}

/// Redis订阅器，负责从Redis获取交易数据
pub struct RedisSubscriber {
    redis_url: String,
    transaction_builder: Arc<TransactionBuilder>,
    transaction_sender: Arc<TransactionSender>,
    #[allow(dead_code)]
    ata_cache: Arc<AtaCache>,
    processed_sigs: Arc<DashMap<String, std::time::Instant>>,
    #[allow(dead_code)]
    trade_log_tx: mpsc::Sender<TradeRecord>,
    transaction_tracker: Arc<TransactionTracker>,
    /// 向交易跟踪服务发送新交易的通道
    tracker_tx: mpsc::Sender<TrackRequest>,
    price_broadcast_manager: PriceBroadcastManager,
}

impl RedisSubscriber {
    /// 创建新的Redis订阅器
    pub async fn new(
        redis_url: &str,
        transaction_builder: Arc<TransactionBuilder>,
        transaction_sender: Arc<TransactionSender>,
        ata_cache: Arc<AtaCache>,
        trade_log_tx: mpsc::Sender<TradeRecord>,
        transaction_tracker: Arc<TransactionTracker>,
        tracker_tx: mpsc::Sender<TrackRequest>,
        price_broadcast_manager: PriceBroadcastManager,
    ) -> Result<Self> {
        info!("正在初始化Redis订阅器，URL: {}", redis_url);
        let client = redis::Client::open(redis_url)?;
        let mut con = client.get_multiplexed_async_connection().await?;
        let pong: String = redis::cmd("PING").query_async(&mut con).await?;
        if pong != "PONG" {
            return Err(anyhow!("Redis PING测试失败，收到: {}", pong));
        }
        info!("Redis连接成功");

        Ok(Self {
            redis_url: redis_url.to_string(),
            transaction_builder,
            transaction_sender,
            ata_cache,
            processed_sigs: Arc::new(DashMap::new()),
            trade_log_tx,
            transaction_tracker,
            tracker_tx,
            price_broadcast_manager,
        })
    }
    
    /// 启动订阅处理
    pub async fn start_subscription(
        &self,
        filter: Arc<ArcSwap<Filter>>,
    ) -> Result<()> {
        let client = redis::Client::open(self.redis_url.as_ref())
            .map_err(|e| anyhow!("创建Redis客户端失败: {}", e))?;

        let mut pubsub_conn = client
            .get_async_pubsub()
            .await
            .map_err(|e| anyhow!("获取PubSub连接失败: {}", e))?;

        pubsub_conn
            .subscribe(TRADES_CHANNEL)
            .await
            .map_err(|e| anyhow!("订阅频道 '{}' 失败: {}", TRADES_CHANNEL, e))?;

        info!("已成功订阅Redis频道: {}", TRADES_CHANNEL);

        let mut msg_stream = pubsub_conn.on_message();
        
        // 初始化一个无状态的计算器实例，可在循环中重复使用
        let calculator = Calculator::new();
        let user_wallet_pubkey = self.transaction_builder.get_wallet_pubkey();

        while let Some(msg) = msg_stream.next().await {
            let total_process_start = Instant::now();
            let payload: Vec<u8> = msg.get_payload_bytes().to_vec();

            // === 协议检测和分发 ===
            let protocol = detect_protocol(&payload);

            match protocol {
                Protocol::Pump => {
                    // Pump热路径 - 现有逻辑完全保持不变
                    self.process_pump_trades(&payload, &user_wallet_pubkey, &filter, &calculator).await;
                }
                Protocol::Bonk => {
                    // Bonk热路径 - 新增处理逻辑
                    self.process_bonk_trades(&payload, &filter).await;
                }
                Protocol::Unknown => {
                    debug!("收到未知协议的消息，跳过处理");
                    continue;
                }
            }

        }
        Ok(())
    }

    /// 处理Pump协议交易 - 现有逻辑完全保持不变
    async fn process_pump_trades(
        &self,
        payload: &[u8],
        user_wallet_pubkey: &Pubkey,
        filter: &Arc<ArcSwap<Filter>>,
        calculator: &Calculator,
    ) {
        let parse_start = Instant::now();
        let trades = parse_trades_from_redis_bytes(payload, user_wallet_pubkey);
        let parse_duration = parse_start.elapsed();

        if !trades.is_empty() {
            // 日志记录整个批次的处理时间
            debug!(
                "Pump批处理: 在 {:?} 内解析出 {} 笔交易",
                parse_duration,
                trades.len()
            );

            // 遍历并独立处理每一笔交易
            for mut trade in trades {
                // --- 为交易分配唯一ID ---
                trade.trade_id = Uuid::new_v4().to_string();

                let total_process_start_for_trade = Instant::now();

                // --- 签名去重 (早期退出) ---
                if !trade.signature.is_empty() {
                    const TTL_SECS: u64 = 120;
                    let now = std::time::Instant::now();
                    if let Some(entry) = self.processed_sigs.get(&trade.signature) {
                        if now.duration_since(*entry.value()) < std::time::Duration::from_secs(TTL_SECS) {
                            continue; // 重复签名且未过期，跳过
                        }
                    }
                    // 写入/刷新签名时间
                    self.processed_sigs.insert(trade.signature.clone(), now);
                }

                // --- 新架构：广播价格更新 ---
                self.price_broadcast_manager.broadcast(trade.mint_pubkey, trade.price);

                // 如果是我们自己钱包的真实交易，则通知跟踪器确认
                if trade.signer == user_wallet_pubkey.to_string() {
                     if let Ok(_signature) = Signature::from_str(&trade.signature) {
                        let tracker_clone = self.transaction_tracker.clone();
                        let trade_for_confirmation = trade.clone();
                        tokio::spawn(async move {
                            tracker_clone.confirm_transaction(trade_for_confirmation).await;
                        });
                    }
                }

                // 无锁读取: 获取筛选器配置的当前快照
                let current_filter = filter.load();

                // --- 步骤1：通用筛选 ---
                // 检查这笔交易是否来自我们跟踪的钱包，无论买卖
                if let Some(config) = current_filter.get_config_if_tracked(&trade) {

                    // --- 步骤2：按类型分流 ---
                    match trade.trade_type {
                        TradeType::Sell => {
                            // 对于卖出交易，只记录日志。持仓同步逻辑已移除。
                            let decimals = 6;
                            let ui_token_amount = trade.token_amount as f64 / 10f64.powi(decimals);

                            let token_str = format!("{} tokens", ui_token_amount).red();
                            let sol_str = format!("{:.6} SOL", trade.sol_cost).red();

                            info!(
                                "观察到Pump卖出: {}, 获得 {} (来自跟踪的钱包: {}, 签名: {})",
                                token_str,
                                sol_str,
                                trade.signer,
                                trade.signature
                            );
                        }
                        TradeType::Buy => {
                            // 对于买入交易，我们进行详细的计算和决策
                            let filter_start = Instant::now();
                            if let Some(buy_amount_sol) = current_filter.check_and_get_buy_details(&trade, config) {
                                let filter_duration = filter_start.elapsed();

                                // --- 纯计算逻辑 ---
                                let calculation_start = Instant::now();
                                let input = CalculateInput {
                                    buy_amount_sol,
                                    trade_price: trade.price,
                                    config,
                                };
                                let calculation_result = calculator.calculate(input);
                                let calculation_duration = calculation_start.elapsed();

                                if calculation_result.should_trade {

                                    // --- 在构建前，使用钱包配置更新交易信息 ---
                                    // 计算并设置正确的滑点 (基点)
                                    trade.slippage_bps = (config.slippage_percentage * 100.0) as u64;

                                    // --- 构建交易并计时 ---
                                    let ui_token_amount = trade.token_amount / 10u64.pow(6);

                                    let ts = Local::now().format("%H:%M:%S.%3f");
                                    info!(
                                        "{} 收到Pump买入交易 (原始: {} SOL / {} Tokens, 来自钱包: {}, 签名: {})",
                                        ts,
                                        trade.sol_cost.to_string().cyan(),
                                        ui_token_amount.to_string().cyan(),
                                        truncate_address(&trade.signer).yellow(),
                                        trade.signature.blue()
                                    );

                                    // 将Token数量从UI单位转换为最小单位（假设decimals=6）
                                    let target_token_amount_raw = calculation_result.min_expected_token_amount_out * 1_000_000u64;

                                    // --- 核心热路径计时与执行 ---
                                    let build_start = Instant::now();
                                    // 根据加速器配置决定是否添加小费账户
                                    let accel_cfg = get_accelerator_config();
                                    let tip_pubkey_opt = if accel_cfg.enabled {
                                        match accel_cfg.provider.as_str() {
                                            "oslot" => Pubkey::from_str(OslotSender::get_random_tip_account()).ok(),
                                            "flashblock" => Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(),
                                            _ => None,
                                        }
                                    } else { None };

                                    // We must have a tip account to proceed with this optimized builder
                                    if let Some(tip_pubkey) = tip_pubkey_opt {
                                        let max_sol_lamports = (calculation_result.final_buy_amount_sol * 1_000_000_000.0) as u64;
                                        let unsigned_tx_result = self.transaction_builder.build_buy_transaction(
                                            &trade,
                                            target_token_amount_raw,
                                            max_sol_lamports,
                                            config,
                                            tip_pubkey,
                                        );
                                        let build_finish = Instant::now();

                                        let build_duration = build_finish.duration_since(build_start);
                                        let total_duration = total_process_start_for_trade.elapsed();

                                        let send_start = Instant::now();

                                        // Log calculation results with mode-specific text
                                        let mode_text = match config.follow_mode {
                                            crate::shared::types::FollowMode::Percentage => "Pump百分比跟单",
                                            crate::shared::types::FollowMode::FixedAmount => "Pump固定金额跟单",
                                        };
                                        warn!(
                                            "{}: 目标代币 {}, 最大花费 {} SOL | 延迟: {:?} (筛选: {:?}, 计算: {:?}, 构建: {:?}, 构建→发送: {:?})",
                                            mode_text,
                                            calculation_result.min_expected_token_amount_out.to_string().green(),
                                            calculation_result.final_buy_amount_sol.to_string().green(),
                                            total_duration,
                                            filter_duration,
                                            calculation_duration,
                                            build_duration,
                                            send_start.duration_since(build_finish)
                                        );

                                        if let Ok(tx) = unsigned_tx_result {
                                            // Sign and log details
                                            let (signed_tx, signature_from_builder, _recent_blockhash) = self.transaction_builder.sign_and_log_details(tx).await.unwrap();

                                            // Send the transaction
                                            let send_result = self.transaction_sender.send_transaction(&signed_tx).await;

                                            let send_duration = send_start.elapsed();

                                            let (status_str, final_signature) = match send_result {
                                                Ok(sig_str) => ("成功".green(), sig_str),
                                                Err(e) => {
                                                    error!("❌ Pump交易发送失败: {}", e);
                                                    ("失败".red(), signature_from_builder.to_string())
                                                }
                                            };

                                            info!(
                                                "✅ 已提交Pump交易，签名: {} | 状态: {} | 耗时: {:?}",
                                                final_signature.blue(),
                                                status_str,
                                                send_duration
                                            );

                                            // 无论发送成功与否，都开始跟踪
                                            let track_req = TrackRequest {
                                                trade_type: TradeType::Buy,
                                                signature: Signature::from_str(&final_signature).unwrap_or(signature_from_builder),
                                                mint: trade.mint_pubkey.to_string(),
                                                sol_amount: trade.sol_cost,
                                                token_amount: trade.token_amount,
                                                user_wallet: self.transaction_builder.get_wallet_pubkey().to_string(),
                                                entry_sol_amount_usd: None,
                                                trade_info: Some(trade.clone()),
                                                wallet_config: Some(config.clone()),
                                            };
                                            if let Err(e) = self.tracker_tx.send(track_req).await {
                                                error!("无法将Pump交易发送到跟踪通道: {}", e);
                                            }

                                        } else if let Err(e) = unsigned_tx_result {
                                            error!("构建Pump交易失败: {}", e);
                                        }
                                    } else {
                                        warn!("Pump: 加速器已启用但未找到有效的小费账户，跳过交易构建。");
                                    }

                                } else {
                                    debug!("Pump交易在计算阶段被拒绝 (签名者: {}), 原因: {}, 耗时: {:?}", trade.signer, calculation_result.reason, calculation_duration);
                                }
                            } else {
                                // 交易在购买细节检查阶段被拒绝
                                debug!(
                                    "Pump购买交易被拒绝 (签名者: {}, MINT: {}), 价格: {}",
                                    trade.signer, trade.mint_pubkey.to_string(), trade.price
                                );
                            }
                        }
                        TradeType::Unknown => {
                            // 忽略未知类型的交易
                        }
                        _ => {
                            // 忽略其他类型的交易（如BonkBuy, BonkSell）
                        }
                    }
                }
                // 如果 `get_config_if_tracked` 返回 None，则交易会被安静地忽略
            }
        } else {
            // 只有在解析结果为空时才记录，避免在每个不匹配的交易上都产生错误日志
            debug!(
                "Pump处理完成, 但未发现有效或匹配的交易"
            );
        }
    }

    /// 处理Bonk协议交易 - 新增处理逻辑（暂时只做日志记录）
    async fn process_bonk_trades(
        &self,
        payload: &[u8],
        _filter: &Arc<ArcSwap<Filter>>,
    ) {
        info!("收到Bonk协议消息，长度: {} 字节", payload.len());

        // 暂时只记录前100个字符用于调试
        let preview = if payload.len() > 100 {
            format!("{}...", String::from_utf8_lossy(&payload[..100]))
        } else {
            String::from_utf8_lossy(payload).to_string()
        };

        debug!("Bonk消息预览: {}", preview);

        // TODO: 在后续阶段实现完整的bonk处理逻辑
        // 1. 解析bonk数据
        // 2. 策略判断
        // 3. 构建bonk交易
        // 4. 发送交易
    }
}