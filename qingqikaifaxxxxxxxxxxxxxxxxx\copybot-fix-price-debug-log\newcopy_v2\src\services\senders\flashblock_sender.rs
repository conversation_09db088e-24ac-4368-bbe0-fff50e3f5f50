use anyhow::{anyhow, Result};
use async_trait::async_trait;
use reqwest::{header::{AUTHORIZATION, CONTENT_TYPE}, Client};
use serde::Deserialize;
use serde_json::json;
use solana_sdk::transaction::Transaction;
use std::cell::RefCell;
use base64::engine::general_purpose::STANDARD as B64_ENGINE;
use base64::Engine;
use tracing::{error, info, warn};
use rand::seq::SliceRandom;

use crate::config::AcceleratorConfig;
use super::{TransactionSenderBackend, rpc_sender::RpcSender};

// --- 硬编码的小费地址 ---
pub const FLASHBLOCK_TIP_ADDRESSES: [&str; 10] = [
    "FLaShB3iXXTWE1vu9wQsChUKq3HFtpMAhb8kAh1pf1wi",
    "FLashhsorBmM9dLpuq6qATawcpqk1Y2aqaZfkd48iT3W",
    "FLaSHJNm5dWYzEgnHJWWJP5ccu128Mu61NJLxUf7mUXU",
    "FLaSHR4Vv7sttd6TyDF4yR1bJyAxRwWKbohDytEMu3wL",
    "FLASHRzANfcAKDuQ3RXv9hbkBy4WVEKDzoAgxJ56DiE4",
    "FLasHstqx11M8W56zrSEqkCyhMCCpr6ze6Mjdvqope5s",
    "FLAShWTjcweNT4NSotpjpxAkwxUr2we3eXQGhpTVzRwy",
    "FLasHXTqrbNvpWFB6grN47HGZfK6pze9HLNTgbukfPSk",
    "FLAshyAyBcKb39KPxSzXcepiS8iDYUhDGwJcJDPX4g2B",
    "FLAsHZTRcf3Dy1APaz6j74ebdMC6Xx4g6i9YxjyrDybR"
];

thread_local! {
    static SER_BUF: RefCell<Vec<u8>> = RefCell::new(Vec::with_capacity(2048));
    static B64_BUF: RefCell<String> = RefCell::new(String::with_capacity(4096));
}

#[derive(Deserialize, Debug)]
struct FlashblockData {
    signatures: Vec<String>,
}

#[derive(Deserialize, Debug)]
struct FlashblockResponse {
    success: bool,
    data: Option<FlashblockData>,
    message: Option<String>,
}

pub struct FlashblockSender {
    client: Client,
    config: AcceleratorConfig,
    fallback_sender: RpcSender,
}

impl FlashblockSender {
    pub fn new(client: Client, config: AcceleratorConfig, fallback_sender: RpcSender) -> Self {
        Self {
            client,
            config,
            fallback_sender,
        }
    }

    pub fn get_random_tip_account() -> &'static str {
        let mut rng = rand::thread_rng();
        FLASHBLOCK_TIP_ADDRESSES.choose(&mut rng).unwrap()
    }
}

#[async_trait]
impl TransactionSenderBackend for FlashblockSender {
    async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        // 从配置中获取激活的提供商URL和Key
        let (api_url, api_key) = match self.config.get_active_provider_config() {
            Some(config) => config,
            None => {
                // 如果配置无效或未启用，直接回退
                warn!("Flashblock 加速器未启用或提供商配置无效。将回退到标准RPC。");
                return self.fallback_sender.send_transaction(tx).await;
            }
        };
        
        let tx_base64 = SER_BUF.with(|ser_cell| {
            B64_BUF.with(|b64_cell| {
                let mut ser_buf = ser_cell.borrow_mut();
                ser_buf.clear();
                bincode::serialize_into(&mut *ser_buf, tx)
                    .map_err(|e| anyhow!("bincode 序列化失败: {e}"))?;
                let mut b64_buf = b64_cell.borrow_mut();
                b64_buf.clear();
                B64_ENGINE.encode_string(ser_buf.as_slice(), &mut *b64_buf);
                Ok::<_, anyhow::Error>(b64_buf.clone())
            })
        })?;

        let tip_account = Self::get_random_tip_account();
        let body = json!({
            "transactions": [ tx_base64 ],
            "tip_account": tip_account,
        });

        let resp = self.client
            .post(&api_url)
            .header(CONTENT_TYPE, "application/json")
            .header(AUTHORIZATION, &api_key)
            .json(&body)
            .send()
            .await;
        
        let resp = match resp {
            Ok(r) => r,
            Err(e) => {
                error!("发送到Flashblock时发生网络错误: {}。将回退到标准RPC。", e);
                return self.fallback_sender.send_transaction(tx).await;
            }
        };

        let status = resp.status();
        let text = resp.text().await?;

        if !status.is_success() {
            warn!("Flashblock 加速器HTTP状态非成功: {} – {}。将回退到标准RPC。", status, text);
            return self.fallback_sender.send_transaction(tx).await;
        }

        match serde_json::from_str::<FlashblockResponse>(&text) {
            Ok(response) => {
                if response.success {
                    if let Some(data) = response.data {
                        if let Some(tx_id) = data.signatures.get(0) {
                            info!("⚡️ 交易已通过 Flashblock 加速器提交: {}", tx_id);
                            return Ok(tx_id.clone());
                        }
                    }
                    warn!("Flashblock 响应成功，但未找到交易ID，将回退到标准RPC发送。");
                    self.fallback_sender.send_transaction(tx).await
                } else {
                    let message = response.message.unwrap_or_else(|| "未知错误".to_string());
                    error!("Flashblock 加速器返回失败: {}。将回退到标准RPC发送。", message);
                    self.fallback_sender.send_transaction(tx).await
                }
            }
            Err(e) => {
                error!("解析 Flashblock 响应失败: {}。响应原文: '{}'。将回退到标准RPC发送。", e, text);
                self.fallback_sender.send_transaction(tx).await
            }
        }
    }
} 