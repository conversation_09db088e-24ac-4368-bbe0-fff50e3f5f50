use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::time::{Duration, Instant};
use std::collections::HashMap;

use anyhow::{Result, anyhow};
use crossbeam_channel::unbounded;
use log::{info, error};

use yellowstone_grpc_proto::prelude::*;
use yellowstone_grpc_client::GeyserGrpcClient;

use crate::core::network::grpc_receiver::GrpcReceiver;
use crate::core::processing::parser_pool::ParserPool;
use crate::core::storage::publisher_pool::PublisherPool;
use crate::core::unified_publisher::UnifiedPublisherPool;
use crate::core::types::*;
use crate::core::parsers::RawTransaction;

/// 终极零延迟流水线引擎
pub struct UltimatePipeline {
    config: PipelineConfig,
    metrics: Arc<RealTimeMetrics>,
    running: Arc<AtomicBool>,
    
    // 队列
    parse_queue: (crossbeam_channel::Sender<RawTransaction>, crossbeam_channel::Receiver<RawTransaction>),
    publish_queue: (crossbeam_channel::Sender<ParsedEvent>, crossbeam_channel::Receiver<ParsedEvent>),
    
    // 组件
    grpc_receiver: Option<GrpcReceiver>,
    parser_pool: Option<ParserPool>,
    publisher_pool: Option<PublisherPool>,
    unified_publisher_pool: Option<UnifiedPublisherPool>,
}

impl UltimatePipeline {
    pub fn new(config: PipelineConfig) -> Self {
        let metrics = Arc::new(RealTimeMetrics::new());
        let running = Arc::new(AtomicBool::new(false));
        
        // 创建无锁队列
        let parse_queue = unbounded::<RawTransaction>();
        let publish_queue = unbounded::<ParsedEvent>();
        
        Self {
            config,
            metrics,
            running,
            parse_queue,
            publish_queue,
            grpc_receiver: None,
            parser_pool: None,
            publisher_pool: None,
            unified_publisher_pool: None,
        }
    }
    
    /// 启动流水线，带自动重启机制
    pub async fn start(&mut self) -> Result<()> {
        self.running.store(true, Ordering::Relaxed);
        
        info!("🚀 启动终极零延迟流水线");
        info!("⚙️  配置: 解析线程={}, 发布线程={}, 队列容量={}", 
            self.config.parser_threads, 
            self.config.publisher_threads, 
            self.config.queue_capacity
        );
        
        // 启动监控
        self.metrics.start_reporting();
        
        // 创建并启动发布器集群
        let publisher_pool = PublisherPool::new(
            self.publish_queue.1.clone(),
            self.config.clone(),
            Arc::clone(&self.metrics),
        );
        self.publisher_pool = Some(publisher_pool);

        // 创建并启动统一发布器集群
        info!("🔧 创建统一发布器集群...");
        let unified_publisher_pool = UnifiedPublisherPool::new(
            self.config.clone(),
            Arc::clone(&self.metrics),
        );
        let unified_sender = unified_publisher_pool.get_sender();
        self.unified_publisher_pool = Some(unified_publisher_pool);
        info!("✅ 统一发布器集群已创建");

        // 创建并启动解析器集群（在统一发布器之后，以便传递发送端）
        let parser_pool = ParserPool::new(
            self.parse_queue.1.clone(),
            self.publish_queue.0.clone(),
            self.config.clone(),
            Arc::clone(&self.metrics),
            Some(unified_sender),
        );
        self.parser_pool = Some(parser_pool);
        
        // 创建gRPC接收器
        let grpc_receiver = GrpcReceiver::new(
            self.parse_queue.0.clone(),
            Arc::clone(&self.metrics),
        );
        self.grpc_receiver = Some(grpc_receiver);
        
        info!("✅ 所有组件已启动，开始处理交易");
        
        // 启动gRPC接收器，带自动重试
        let endpoint = &get_config().grpc.endpoint;
        
        // 无限重试循环
        loop {
            if !self.running.load(Ordering::Relaxed) {
                break;
            }
            
            info!("🔌 启动gRPC接收器...");
            if let Some(ref receiver) = self.grpc_receiver {
                match receiver.start_with_retry(endpoint).await {
                    Ok(_) => {
                        info!("✅ gRPC接收器正常退出");
                        break;
                    }
                    Err(e) => {
                        error!("❌ gRPC接收器失败: {}", e);
                        
                        if self.running.load(Ordering::Relaxed) {
                            info!("⏳ {}秒后重启gRPC接收器...", get_config().grpc.reconnect_interval);
                            tokio::time::sleep(tokio::time::Duration::from_secs(get_config().grpc.reconnect_interval)).await;
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 优雅关闭流水线
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("🛑 正在关闭流水线...");
        self.running.store(false, Ordering::Relaxed);
        
        // 等待所有工作线程完成
        if let Some(parser_pool) = self.parser_pool.take() {
            for worker in parser_pool.workers {
                let _ = worker.join();
            }
        }
        
        if let Some(publisher_pool) = self.publisher_pool.take() {
            for worker in publisher_pool.workers {
                let _ = worker.join();
            }
        }
        
        info!("✅ 流水线已关闭");
        Ok(())
    }
    
    /// 获取监控指标
    pub fn get_metrics(&self) -> &RealTimeMetrics {
        &self.metrics
    }
    
    /// 检查是否运行中
    pub fn is_running(&self) -> bool {
        self.running.load(Ordering::Relaxed)
    }
    
    /// 设置运行状态
    pub fn set_running(&self, running: bool) {
        self.running.store(running, Ordering::Relaxed);
    }

    /// 获取统一发布器发送端
    pub fn get_unified_publisher_sender(&self) -> Option<crossbeam_channel::Sender<UnifiedEvent>> {
        self.unified_publisher_pool.as_ref().map(|pool| pool.get_sender())
    }
}