use crate::shared::types::Protocol;

/// 协议检测函数 - 零延迟实现
/// 
/// 通过检查消息的前几个字节快速识别协议类型
/// 性能要求：单次检测 < 10纳秒
#[inline(always)]
pub fn detect_protocol(payload: &[u8]) -> Protocol {
    if payload.is_empty() {
        return Protocol::Unknown;
    }
    
    // Bonk检测：以 'signature"' 开头
    if payload.len() >= 10 && payload.starts_with(b"signature\"") {
        Protocol::Bonk
    }
    // Pump检测：包含 'TYPE:'
    else if payload.len() >= 5 && contains_type_marker(payload) {
        Protocol::Pump
    }
    else {
        Protocol::Unknown
    }
}

/// 快速检查是否包含TYPE:标记
/// 使用优化的字节搜索，避免完整扫描
#[inline(always)]
fn contains_type_marker(payload: &[u8]) -> bool {
    // 使用memchr库进行快速字节搜索
    if let Some(pos) = memchr::memchr(b'T', payload) {
        // 检查是否是 "TYPE:" 模式
        if pos + 4 < payload.len() {
            &payload[pos..pos + 5] == b"TYPE:"
        } else {
            false
        }
    } else {
        false
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_bonk_detection() {
        let bonk_payload = br#"signature": "51YVS2wAfniiVwKxSUKiC7tP2WNEmNzhdaRS8G7giz7yv5R9i25GpAMZPLfDvDyKNhn5G2cPHQ5QMcrWSMdemM1A","#;
        assert_eq!(detect_protocol(bonk_payload), Protocol::Bonk);
    }

    #[test]
    fn test_pump_detection() {
        let pump_payload = b"TYPE: Buy\nSIGNATURE: abc123\n";
        assert_eq!(detect_protocol(pump_payload), Protocol::Pump);
    }

    #[test]
    fn test_unknown_detection() {
        let unknown_payload = b"some random data";
        assert_eq!(detect_protocol(unknown_payload), Protocol::Unknown);
    }

    #[test]
    fn test_empty_payload() {
        let empty_payload = b"";
        assert_eq!(detect_protocol(empty_payload), Protocol::Unknown);
    }

    #[test]
    fn test_performance_benchmark() {
        use std::time::Instant;
        
        let bonk_payload = br#"signature": "51YVS2wAfniiVwKxSUKiC7tP2WNEmNzhdaRS8G7giz7yv5R9i25GpAMZPLfDvDyKNhn5G2cPHQ5QMcrWSMdemM1A","#;
        let pump_payload = b"TYPE: Buy\nSIGNATURE: abc123\n";
        
        // 预热
        for _ in 0..1000 {
            detect_protocol(bonk_payload);
            detect_protocol(pump_payload);
        }
        
        // 性能测试
        let iterations = 1_000_000;
        let start = Instant::now();
        
        for _ in 0..iterations {
            detect_protocol(bonk_payload);
            detect_protocol(pump_payload);
        }
        
        let elapsed = start.elapsed();
        let avg_ns = elapsed.as_nanos() / (iterations * 2);
        
        println!("平均协议检测时间: {} 纳秒", avg_ns);
        assert!(avg_ns < 50, "协议检测时间超过50纳秒: {} ns", avg_ns);
    }
}
