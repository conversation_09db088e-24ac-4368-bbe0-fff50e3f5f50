# 钱包管理系统前端 2.0 - Docker镜像

## 镜像信息

- **镜像名称**: wallet-admin-frontend:2.0
- **文件名称**: wallet-admin-frontend-2.0.tar
- **文件大小**: 21.4 MB
- **创建时间**: 2025-06-22
- **版本标识**: 前端2.0

## 功能特性

### 核心功能
- ✅ 用户登录认证（集成后端API）
- ✅ 钱包配置管理
- ✅ 详细交易统计面板
- ✅ 策略分布统计（可点击查看钱包详情）
- ✅ 实时交易记录
- ✅ 钱包备注管理

### 新增功能（2.0版本）
- 🆕 **后端API集成**: 使用真实的登录API `/api/v1/auth/login`
- 🆕 **详细交易统计**: 包含基础统计、金额统计、持仓分析
- 🆕 **交易统计置顶**: 优化仪表板布局，重要信息优先显示
- 🆕 **策略钱包弹窗**: 点击策略分布可查看对应钱包地址和备注
- 🆕 **登录体验优化**: 移除测试账户提示，更简洁的界面

### 技术栈
- **前端框架**: React 18 + TypeScript
- **UI组件库**: Ant Design 5.x
- **状态管理**: Zustand
- **构建工具**: Vite
- **HTTP客户端**: Axios
- **路由管理**: React Router 7.x

## 部署说明

### 导入镜像
```bash
# 导入Docker镜像
docker load -i wallet-admin-frontend-2.0.tar

# 验证镜像导入
docker images | grep wallet-admin-frontend
```

### 运行容器
```bash
# 快速启动
docker run -d -p 3000:80 --name wallet-frontend-2.0 wallet-admin-frontend:2.0

# 或使用自定义端口
docker run -d -p 8080:80 --name wallet-frontend-2.0 wallet-admin-frontend:2.0
```

### 访问应用
- **应用地址**: http://localhost:3000
- **登录页面**: http://localhost:3000/login

## 配置要求

### 系统要求
- Docker 20.10+
- 至少 512MB 可用内存
- 至少 100MB 可用磁盘空间

### 网络配置
- **前端端口**: 80 (容器内部)
- **映射端口**: 3000 (可自定义)
- **API代理**: 自动代理 `/api/*` 到 `http://host.docker.internal:8080`

### 环境变量
- `NODE_ENV=production`
- 后端API地址通过Nginx代理配置

## 登录账户

支持以下测试账户（需要后端服务运行）：
- **管理员**: admin / 123456
- **交易员**: trader / trader123  
- **普通用户**: user1 / password1

## 更新内容

### v2.0 主要更新
1. **API集成**: 完全集成后端登录API
2. **统计增强**: 大幅扩展交易统计功能
3. **交互优化**: 策略分布支持点击查看详情
4. **布局改进**: 交易统计置顶显示
5. **体验提升**: 简化登录界面

### 技术改进
- 优化Docker构建流程
- 改进Nginx配置支持SSE
- 增强错误处理机制
- 提升响应式布局适配

## 故障排除

### 常见问题
1. **无法访问**: 检查端口映射和防火墙设置
2. **登录失败**: 确认后端服务在8080端口运行
3. **API错误**: 检查网络连接和后端服务状态

### 日志查看
```bash
# 查看容器日志
docker logs wallet-frontend-2.0

# 实时查看日志
docker logs -f wallet-frontend-2.0
```

## 技术支持

- **镜像版本**: 2.0
- **基础镜像**: nginx:alpine
- **构建方式**: 多阶段构建
- **优化特性**: Gzip压缩、静态资源缓存

## 备注

此镜像为生产就绪版本，包含所有必要的优化配置。建议在生产环境中配合反向代理和HTTPS使用。
