<template>
  <div class="mirrors-page">
    <div class="page-title">
      <h2>镜像管理</h2>
      <p>管理所有注册的镜像端设备</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="content-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="设备ID">
          <el-input v-model="searchForm.deviceId" placeholder="输入设备ID" style="width: 200px;" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="在线" value="online" />
            <el-option label="离线" value="offline" />
            <el-option label="警告" value="warning" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 镜像列表 -->
    <div class="content-card">
      <div class="card-header">
        <h3 class="card-title">镜像端列表</h3>
        <div>
          <el-button type="primary" @click="showBatchControl = true">
            批量操作
          </el-button>
        </div>
      </div>

      <el-table 
        :data="filteredMirrors" 
        style="width: 100%" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="设备ID" width="120" />
        
        <el-table-column prop="name" label="设备名称" />
        
        <el-table-column prop="version" label="版本" width="100" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastHeartbeat" label="最后心跳" width="150" />
        
        <el-table-column prop="runningTime" label="运行时长" width="120" />
        
        <el-table-column prop="dailyLimit" label="日限时间" width="120" />
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)">详情</el-button>
            <el-button 
              size="small" 
              :type="row.status === 'online' ? 'danger' : 'primary'"
              @click="toggleStatus(row)"
            >
              {{ row.status === 'online' ? '停止' : '启动' }}
            </el-button>
            <el-button size="small" type="warning" @click="setTimeLimit(row)">
              时限
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量操作对话框 -->
    <el-dialog v-model="showBatchControl" title="批量操作" width="500px">
      <div v-if="selectedMirrors.length === 0">
        <el-alert message="请先选择要操作的镜像端" type="warning" :closable="false" />
      </div>
      <div v-else>
        <p>已选择 {{ selectedMirrors.length }} 个镜像端：</p>
        <el-tag v-for="mirror in selectedMirrors" :key="mirror.id" style="margin: 2px;">
          {{ mirror.name }}
        </el-tag>
        
        <div style="margin: 20px 0;">
          <el-button type="primary" @click="batchStart">批量启动</el-button>
          <el-button type="danger" @click="batchStop">批量停止</el-button>
          <el-button type="warning" @click="showBatchTimeLimit = true">设置时限</el-button>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showBatchControl = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 时间限制设置对话框 -->
    <el-dialog v-model="showTimeLimit" title="设置运行时间限制" width="400px">
      <el-form :model="timeLimitForm" label-width="120px">
        <el-form-item label="每日限制时间">
          <el-input-number 
            v-model="timeLimitForm.dailyHours" 
            :min="1" 
            :max="24" 
            controls-position="right"
          />
          <span style="margin-left: 8px;">小时</span>
        </el-form-item>
        
        <el-form-item label="允许时间段">
          <el-time-picker
            v-model="timeLimitForm.allowedTime"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showTimeLimit = false">取消</el-button>
        <el-button type="primary" @click="confirmTimeLimit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { adminAPI } from '@/api'

const loading = ref(false)
const showBatchControl = ref(false)
const showTimeLimit = ref(false)
const showBatchTimeLimit = ref(false)
const selectedMirrors = ref([])
const currentMirror = ref(null)

// 搜索表单
const searchForm = reactive({
  deviceId: '',
  status: ''
})

// 时间限制表单
const timeLimitForm = reactive({
  dailyHours: 8,
  allowedTime: []
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 镜像数据
const mirrors = ref([])

// 过滤后的镜像数据
const filteredMirrors = computed(() => {
  let filtered = mirrors.value
  
  if (searchForm.deviceId) {
    filtered = filtered.filter(m => 
      m.id.toLowerCase().includes(searchForm.deviceId.toLowerCase()) ||
      m.name.toLowerCase().includes(searchForm.deviceId.toLowerCase())
    )
  }
  
  if (searchForm.status) {
    filtered = filtered.filter(m => m.status === searchForm.status)
  }
  
  pagination.total = filtered.length
  return filtered
})

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    online: 'success',
    offline: 'danger',
    warning: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    online: '在线',
    offline: '离线',
    warning: '警告'
  }
  return texts[status] || '未知'
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  ElMessage.success('搜索完成')
}

// 重置搜索
const resetSearch = () => {
  searchForm.deviceId = ''
  searchForm.status = ''
  pagination.current = 1
  ElMessage.info('已重置搜索条件')
}

// 刷新数据
const refreshData = () => {
  loadMirrorsData()
}

// 查看详情
const viewDetails = (mirror) => {
  ElMessage.info(`查看镜像 ${mirror.name} 详情功能开发中`)
}

// 切换状态
const toggleStatus = async (mirror) => {
  const action = mirror.status === 'online' ? '停止' : '启动'
  try {
    await ElMessageBox.confirm(`确定要${action}镜像 ${mirror.name} 吗？`, '确认操作')
    ElMessage.success(`${action}指令已发送`)
  } catch {
    // 用户取消
  }
}

// 设置时间限制
const setTimeLimit = (mirror) => {
  currentMirror.value = mirror
  showTimeLimit.value = true
}

// 确认时间限制
const confirmTimeLimit = () => {
  ElMessage.success('时间限制设置成功')
  showTimeLimit.value = false
}

// 选择变更
const handleSelectionChange = (selection) => {
  selectedMirrors.value = selection
}

// 批量启动
const batchStart = () => {
  ElMessage.success(`批量启动 ${selectedMirrors.value.length} 个镜像端`)
  showBatchControl.value = false
}

// 批量停止
const batchStop = () => {
  ElMessage.success(`批量停止 ${selectedMirrors.value.length} 个镜像端`)
  showBatchControl.value = false
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
}

const handleCurrentChange = (current) => {
  pagination.current = current
}

onMounted(() => {
  loadMirrorsData()
})

// 加载镜像数据
const loadMirrorsData = async () => {
  loading.value = true
  try {
    const response = await adminAPI.mirrors.list()
    if (response.success) {
      // 转换后端数据格式到前端格式
      mirrors.value = response.data.map(mirror => ({
        id: mirror.mirror_id,
        name: mirror.device_name,
        version: mirror.version,
        status: mirror.status === '已注册' ? 'online' : 'offline',
        lastHeartbeat: mirror.last_heartbeat || '未知',
        runningTime: '0小时',
        dailyLimit: '8/8小时',
        deviceId: mirror.device_id,
        registeredAt: mirror.registered_at
      }))
      ElMessage.success(`加载了 ${mirrors.value.length} 个镜像端`)
    }
  } catch (error) {
    ElMessage.error('加载镜像数据失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.mirrors-page {
  padding: 0;
}

.page-title {
  margin-bottom: 24px;
}

.page-title h2 {
  font-size: 20px;
  color: #333;
  margin: 0 0 4px 0;
}

.page-title p {
  color: #666;
  font-size: 14px;
  margin: 0;
}
</style> 