# Rust编译产物
target/
*/target/
Cargo.lock

# 环境配置文件
.env
.env.local
.env.production
*.env

# 日志文件
*.log
logs/
*/logs/

# IDE配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

# Docker相关
.dockerignore

# 测试覆盖率报告
tarpaulin-report.html
cobertura.xml

# SQLx相关
.sqlx/

# 前端构建产物
node_modules/
dist/
build/
*/node_modules/
*/dist/
*/build/

# 运行时文件
*.pid

# 其他项目特定文件
/admin/target/
/newcopy_v2/target/
/copy-bot/target/ 