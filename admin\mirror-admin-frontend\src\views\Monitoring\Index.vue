<template>
  <div class="monitoring-page">
    <div class="page-title">
      <h2>数据监控</h2>
      <p>实时监控交易数据和系统运行状态</p>
    </div>

    <!-- 实时数据概览 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon" style="background: #e6f7ff; color: #1890ff;">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">今日交易量</div>
          <div class="stats-value">{{ realtimeStats.todayTransactions }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon" style="background: #f6ffed; color: #52c41a;">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">累计手续费</div>
          <div class="stats-value">{{ realtimeStats.totalFees }} ETH</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon" style="background: #fff2e8; color: #faad14;">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">平均响应时间</div>
          <div class="stats-value">{{ realtimeStats.avgResponseTime }}ms</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon" style="background: #f6ffed; color: #52c41a;">
          <el-icon><SuccessFilled /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-title">成功率</div>
          <div class="stats-value">{{ realtimeStats.successRate }}%</div>
        </div>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 交易数据图表 -->
      <el-col :span="16">
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">交易趋势图</h3>
            <div>
              <el-radio-group v-model="chartTimeRange" @change="updateChart">
                <el-radio-button label="1h">1小时</el-radio-button>
                <el-radio-button label="24h">24小时</el-radio-button>
                <el-radio-button label="7d">7天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          
          <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #666;">
            <div>
              <el-icon size="48"><TrendCharts /></el-icon>
              <p>图表组件开发中...</p>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 实时日志 -->
      <el-col :span="8">
        <div class="content-card">
          <div class="card-header">
            <h3 class="card-title">实时日志</h3>
            <el-button size="small" @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <div class="log-container">
            <div v-for="log in realtimeLogs" :key="log.id" class="log-item" :class="log.level">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-content">{{ log.message }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <div class="content-card">
      <div class="card-header">
        <h3 class="card-title">镜像端详细数据</h3>
        <div>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="filterData"
          />
          <el-button type="primary" style="margin-left: 10px;" @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>

      <el-table :data="monitoringData" style="width: 100%" v-loading="loading">
        <el-table-column prop="mirrorId" label="镜像ID" width="120" />
        <el-table-column prop="mirrorName" label="镜像名称" />
        <el-table-column prop="transactions" label="交易数量" width="100" />
        <el-table-column prop="volume" label="交易金额" width="120" />
        <el-table-column prop="fees" label="手续费收益" width="120" />
        <el-table-column prop="avgTime" label="平均耗时" width="100" />
        <el-table-column prop="successRate" label="成功率" width="100">
          <template #default="{ row }">
            <el-tag :type="row.successRate >= 95 ? 'success' : 'warning'" size="small">
              {{ row.successRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdate" label="最后更新" width="150" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)
const chartTimeRange = ref('24h')
const dateRange = ref([])

// 实时统计数据
const realtimeStats = reactive({
  todayTransactions: 0,
  totalFees: 0,
  avgResponseTime: 0,
  successRate: 0
})

// 实时日志
const realtimeLogs = ref([])

// 监控数据
const monitoringData = ref([])

// 更新图表
const updateChart = (range) => {
  ElMessage.info(`切换到${range}数据视图`)
}

// 刷新日志
const refreshLogs = () => {
  ElMessage.success('日志已刷新')
}

// 筛选数据
const filterData = () => {
  ElMessage.info('数据筛选功能开发中')
}

// 导出数据
const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

onMounted(() => {
  // TODO: 初始化实时数据
})
</script>

<style scoped>
.monitoring-page {
  padding: 0;
}

.page-title {
  margin-bottom: 24px;
}

.page-title h2 {
  font-size: 20px;
  color: #333;
  margin: 0 0 4px 0;
}

.page-title p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
}

.log-item {
  margin-bottom: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.log-item.info {
  background: #f0f9ff;
  border-left: 3px solid #1890ff;
}

.log-item.success {
  background: #f6ffed;
  border-left: 3px solid #52c41a;
}

.log-item.warning {
  background: #fff7e6;
  border-left: 3px solid #faad14;
}

.log-item.error {
  background: #fff1f0;
  border-left: 3px solid #ff4d4f;
}

.log-time {
  color: #666;
  font-weight: 500;
}

.log-content {
  color: #333;
  margin-top: 2px;
}
</style> 