import redis
import json
import time
import sys
import signal
import datetime

def current_time():
    """返回当前时间的格式化字符串"""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

class RedisSubscriber:
    def __init__(self, host='127.0.0.1', port=6379, channel='trades_channel'):
        """初始化Redis订阅者"""
        self.redis_host = host
        self.redis_port = port
        self.channel = channel
        self.redis_client = None
        self.pubsub = None
        self.running = False
    
    def connect(self):
        """连接到Redis服务器"""
        try:
            print(f"[{current_time()}] 正在连接Redis服务器 {self.redis_host}:{self.redis_port}...")
            self.redis_client = redis.Redis(host=self.redis_host, port=self.redis_port, decode_responses=True)
            self.redis_client.ping()  # 测试连接
            print(f"[{current_time()}] Redis连接成功")
            
            self.pubsub = self.redis_client.pubsub()
            return True
        except redis.ConnectionError as e:
            print(f"[{current_time()}] 连接Redis服务器失败: {e}")
            return False
        except Exception as e:
            print(f"[{current_time()}] 发生错误: {e}")
            return False
    
    def subscribe(self):
        """订阅指定的Redis频道"""
        if not self.redis_client or not self.pubsub:
            if not self.connect():
                return False
        
        try:
            print(f"[{current_time()}] 正在订阅频道 '{self.channel}'...")
            self.pubsub.subscribe(self.channel)
            print(f"[{current_time()}] 成功订阅频道 '{self.channel}'")
            print(f"[{current_time()}] 正在等待消息... (按Ctrl+C退出)")
            return True
        except Exception as e:
            print(f"[{current_time()}] 订阅频道时出错: {e}")
            return False
    
    def listen(self):
        """监听频道消息"""
        if not self.pubsub:
            print(f"[{current_time()}] 未初始化订阅，请先调用subscribe方法")
            return
        
        self.running = True
        msg_count = 0
        
        try:
            for message in self.pubsub.listen():
                if not self.running:
                    break
                
                # 只处理消息类型为'message'的数据
                if message['type'] == 'message':
                    msg_count += 1
                    channel = message['channel']
                    data = message['data']
                    
                    # 打印消息头信息
                    print(f"\n{'='*80}")
                    print(f"[{current_time()}] 收到消息 #{msg_count} 从频道: {channel}")
                    print(f"{'-'*80}")
                    
                    # 尝试解析JSON，如果不是JSON则直接打印
                    try:
                        json_data = json.loads(data)
                        print(json.dumps(json_data, ensure_ascii=False, indent=2))
                    except json.JSONDecodeError:
                        print(data)
                    
                    print(f"{'='*80}\n")
        except KeyboardInterrupt:
            print(f"\n[{current_time()}] 接收到中断信号，停止监听")
        except Exception as e:
            print(f"\n[{current_time()}] 监听时发生错误: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止监听并清理资源"""
        self.running = False
        if self.pubsub:
            self.pubsub.unsubscribe()
            self.pubsub.close()
        if self.redis_client:
            self.redis_client.close()
        print(f"[{current_time()}] 已关闭Redis连接")

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print(f"\n[{current_time()}] 接收到中断信号，准备退出...")
    sys.exit(0)

if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    # 获取命令行参数
    redis_host = sys.argv[1] if len(sys.argv) > 1 else '127.0.0.1'
    redis_port = int(sys.argv[2]) if len(sys.argv) > 2 else 6379
    channel = sys.argv[3] if len(sys.argv) > 3 else 'trades_channel'
    
    print(f"[{current_time()}] Redis订阅器启动")
    print(f"[{current_time()}] 服务器: {redis_host}:{redis_port}")
    print(f"[{current_time()}] 频道: {channel}")
    
    # 创建并启动订阅者
    subscriber = RedisSubscriber(redis_host, redis_port, channel)
    if subscriber.subscribe():
        subscriber.listen()