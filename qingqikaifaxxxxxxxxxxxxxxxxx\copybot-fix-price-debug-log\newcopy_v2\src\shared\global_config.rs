use crate::config::AcceleratorConfig;
use once_cell::sync::OnceCell;

// 全局静态变量，用于存储加速器配置
static ACCELERATOR_CONFIG: OnceCell<AcceleratorConfig> = OnceCell::new();

/// 初始化全局加速器配置。此函数应在程序启动时调用一次。
pub fn initialize_accelerator_config(config: AcceleratorConfig) {
    if ACCELERATOR_CONFIG.set(config).is_err() {
        // 这将在程序尝试第二次初始化时发生，理论上不应该出现
        panic!("全局加速器配置已初始化，无法再次设置！");
    }
}

/// 获取对全局加速器配置的引用。
/// 如果配置尚未初始化，将导致 panic。
pub fn get_accelerator_config() -> &'static AcceleratorConfig {
    ACCELERATOR_CONFIG.get().expect("全局加速器配置尚未初始化！")
} 