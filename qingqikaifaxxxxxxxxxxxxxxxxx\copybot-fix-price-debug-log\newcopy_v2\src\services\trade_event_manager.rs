// 这是一个新文件: newcopy_v2/src/services/trade_event_manager.rs
use tokio::sync::{mpsc, Mutex};
use tracing::{info, warn, error};
use std::sync::Arc;
use axum::response::sse::Event;
use std::convert::Infallible;
use crate::shared::types::TradeEvent;

/// 用于管理所有Server-Sent Events (SSE)交易事件订阅和广播的服务
#[derive(Debug, Default)]
pub struct TradeEventManager {
    /// 全局订阅者列表，包含所有已连接客户端的发送通道
    subscribers: Vec<mpsc::Sender<Result<Event, Infallible>>>,
}

impl TradeEventManager {
    /// 创建一个新的管理器实例
    pub fn new() -> Arc<Mutex<Self>> {
        Arc::new(Mutex::new(Self::default()))
    }

    /// 注册一个新的订阅者
    pub fn register(&mut self, sender: mpsc::Sender<Result<Event, Infallible>>) {
        info!("新的SSE交易事件流订阅者已注册");
        self.subscribers.push(sender);
    }

    /// 向所有订阅了的客户端广播新的交易事件
    pub fn broadcast(&mut self, trade_event: &TradeEvent) {
        if self.subscribers.is_empty() {
            return;
        }

        let message = match serde_json::to_string(trade_event) {
            Ok(msg) => msg,
            Err(e) => {
                error!("序列化交易事件失败: {}", e);
                return;
            }
        };

        // 使用默认事件类型，提升兼容性（客户端可通过 onmessage 捕获）
        let event = Event::default().data(message);
        
        self.subscribers.retain(|sender| {
            match sender.try_send(Ok(event.clone())) {
                Ok(_) => true, // 保留
                Err(e) => {
                    if let mpsc::error::TrySendError::Closed(_) = e {
                        warn!("检测到SSE客户端断开连接，正在清理交易事件订阅。");
                        false // 从列表中移除
                    } else {
                        // 如果通道已满，暂时保留
                        true 
                    }
                }
            }
        });
    }
} 