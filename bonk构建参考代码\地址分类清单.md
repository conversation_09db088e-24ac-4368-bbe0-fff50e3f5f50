# Solana 交易地址分类清单

根据 Raydium AMM 交易的要求，地址分为三类：直接获取、计算生成、和用户提供。

## 🔍 需要计算的地址清单

### 1. **关联代币账户 (ATA)**
```rust
// 用户的代币账户
let user_token_account = get_associated_token_address(&user_pubkey, &token_mint);

// 用户的 WSOL 账户  
let user_wsol_account = get_associated_token_address(&user_pubkey, &wsol_mint);
```

### 2. **程序派生地址 (PDA)**
```rust
// Raydium AMM 权威地址
let (pool_authority, authority_bump) = Pubkey::find_program_address(
    &[&pool_id.to_bytes()[..32]], 
    &raydium_amm_program_id
);

// Serum 市场权威地址
let (market_authority, market_bump) = Pubkey::find_program_address(
    &[&market_id.to_bytes()[..32]], 
    &serum_program_id
);

// LP 代币权威地址
let (lp_mint_authority, lp_bump) = Pubkey::find_program_address(
    &[&pool_id.to_bytes()[..32], b"lp_mint_authority"], 
    &raydium_amm_program_id
);

// 代币金库权威地址
let (vault_signer, vault_bump) = Pubkey::find_program_address(
    &[&pool_id.to_bytes()[..32], b"vault_signer"], 
    &raydium_amm_program_id
);
```

### 3. **临时账户地址**
```rust
// 创建临时 WSOL 账户（随机生成）
let temp_wsol_keypair = Keypair::new();
let temp_wsol_account = temp_wsol_keypair.pubkey();
```

## 📍 直接获取的地址清单（从链上查询）

### 1. **从池状态账户解析**
```rust
// 从池状态地址获取的信息
struct PoolState {
    pub base_mint: Pubkey,           // 基础代币地址
    pub quote_mint: Pubkey,          // 报价代币地址（通常是 WSOL）
    pub base_vault: Pubkey,          // 基础代币金库
    pub quote_vault: Pubkey,         // 报价代币金库
    pub lp_mint: Pubkey,            // LP 代币地址
    pub open_orders: Pubkey,         // 开放订单账户
    pub target_orders: Pubkey,       // 目标订单账户
    pub withdraw_queue: Pubkey,      // 提现队列
    pub market_id: Pubkey,          // 市场ID
}
```

### 2. **从市场账户解析**
```rust
// 从市场ID获取的信息
struct MarketState {
    pub base_vault: Pubkey,          // 市场基础金库
    pub quote_vault: Pubkey,         // 市场报价金库
    pub bids: Pubkey,               // 买单账户
    pub asks: Pubkey,               // 卖单账户
    pub event_queue: Pubkey,        // 事件队列
    pub req_queue: Pubkey,          // 请求队列
    pub coin_vault: Pubkey,         // 币种金库
    pub pc_vault: Pubkey,           // PC 金库
}
```

## 🏷️ 用户提供的地址清单

### 1. **必须提供**
```rust
// 用户钱包私钥（用于生成公钥）
let user_keypair = Keypair::from_bytes(&private_key_bytes);
let user_pubkey = user_keypair.pubkey();

// 目标代币地址
let token_mint = Pubkey::from_str("代币合约地址")?;

// 池状态地址
let pool_id = Pubkey::from_str("池状态地址")?;
```

### 2. **系统固定地址**
```rust
// 程序ID（固定不变）
const RAYDIUM_AMM_PROGRAM_ID: &str = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
const SERUM_PROGRAM_ID: &str = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin";
const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";

// 系统程序ID
const SYSTEM_PROGRAM_ID: &str = "11111111111111111111111111111111";
const RENT_PROGRAM_ID: &str = "SysvarRent111111111111111111111111111111111";
```

## 📋 完整的地址计算函数

```rust
pub struct TransactionAddresses {
    // 计算生成的地址
    pub user_token_account: Pubkey,
    pub user_wsol_account: Pubkey,
    pub pool_authority: Pubkey,
    pub market_authority: Pubkey,
    pub temp_wsol_account: Pubkey,
    
    // 从链上获取的地址
    pub base_vault: Pubkey,
    pub quote_vault: Pubkey,
    pub market_base_vault: Pubkey,
    pub market_quote_vault: Pubkey,
    pub bids: Pubkey,
    pub asks: Pubkey,
    pub event_queue: Pubkey,
    pub open_orders: Pubkey,
    pub target_orders: Pubkey,
    
    // 用户提供的地址
    pub user_pubkey: Pubkey,
    pub token_mint: Pubkey,
    pub pool_id: Pubkey,
}

impl TransactionAddresses {
    pub async fn new(
        user_keypair: &Keypair,
        token_mint: &Pubkey,
        pool_id: &Pubkey,
        client: &RpcClient,
    ) -> Result<Self> {
        let user_pubkey = user_keypair.pubkey();
        
        // 1. 计算关联代币账户
        let user_token_account = get_associated_token_address(&user_pubkey, token_mint);
        let user_wsol_account = get_associated_token_address(&user_pubkey, &WSOL_MINT);
        
        // 2. 计算程序派生地址
        let (pool_authority, _) = Pubkey::find_program_address(
            &[&pool_id.to_bytes()[..32]], 
            &RAYDIUM_AMM_PROGRAM_ID
        );
        
        // 3. 创建临时账户
        let temp_wsol_keypair = Keypair::new();
        let temp_wsol_account = temp_wsol_keypair.pubkey();
        
        // 4. 从链上获取池状态
        let pool_state = get_pool_state(client, pool_id).await?;
        
        // 5. 从链上获取市场状态
        let (market_authority, _) = Pubkey::find_program_address(
            &[&pool_state.market_id.to_bytes()[..32]], 
            &SERUM_PROGRAM_ID
        );
        let market_state = get_market_state(client, &pool_state.market_id).await?;
        
        Ok(Self {
            // 计算的地址
            user_token_account,
            user_wsol_account,
            pool_authority,
            market_authority,
            temp_wsol_account,
            
            // 从链上获取的地址
            base_vault: pool_state.base_vault,
            quote_vault: pool_state.quote_vault,
            market_base_vault: market_state.base_vault,
            market_quote_vault: market_state.quote_vault,
            bids: market_state.bids,
            asks: market_state.asks,
            event_queue: market_state.event_queue,
            open_orders: pool_state.open_orders,
            target_orders: pool_state.target_orders,
            
            // 用户提供的地址
            user_pubkey,
            token_mint: *token_mint,
            pool_id: *pool_id,
        })
    }
}
```

## ⚠️ 重要说明

### 需要计算的地址特点：
- 基于已知信息和固定规则生成
- 可以离线计算
- 不需要网络查询

### 需要获取的地址特点：
- 存储在链上账户数据中
- 需要通过 RPC 查询
- 可能会变化

### 用户提供的地址特点：
- 由用户输入或配置
- 包括私钥、代币地址、池地址等
- 是交易的基础参数

这样分类可以帮您更好地组织代码结构，优化性能（计算的地址可以并行处理，获取的地址需要串行查询）。