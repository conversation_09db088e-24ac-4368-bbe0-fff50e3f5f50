// newcopy_v2/src/services/price_broadcast.rs
use std::sync::Arc;
use solana_sdk::pubkey::Pubkey;
use tokio::sync::broadcast;
use tracing::debug;

const PRICE_CHANNEL_CAPACITY: usize = 1024;

/// 负责广播价格更新的管理器。
/// 它持有一个广播通道的发送端。
#[derive(Debug, <PERSON>lone)]
pub struct PriceBroadcastManager {
    price_sender: Arc<broadcast::Sender<(Pubkey, f64)>>,
}

impl PriceBroadcastManager {
    /// 创建一个新的 PriceBroadcastManager。
    ///
    /// 返回管理器实例本身和一个可以被克隆给多个订阅者的接收器。
    pub fn new() -> Self {
        let (sender, _) = broadcast::channel(PRICE_CHANNEL_CAPACITY);
        Self {
            price_sender: Arc::new(sender),
        }
    }

    /// 广播一个新的价格更新。
    ///
    /// # Arguments
    ///
    /// * `mint` - 代币的公钥。
    /// * `price` - 最新的价格。
    pub fn broadcast(&self, mint: Pubkey, price: f64) {
        // send 方法在没有接收者时会返回一个错误，但在我们的设计中这是正常现象，
        // 因为可能在价格更新时还没有任何活跃的交易生命周期 Actor。
        // 因此我们忽略这个错误。
        if self.price_sender.send((mint, price)).is_ok() {
            debug!("已广播价格更新: Mint={}, Price={}", mint, price);
        }
    }

    /// 订阅价格更新通道。
    ///
    /// 每个需要接收价格更新的 Actor 都应该调用此方法来获取自己的接收器副本。
    pub fn subscribe(&self) -> broadcast::Receiver<(Pubkey, f64)> {
        self.price_sender.subscribe()
    }
} 