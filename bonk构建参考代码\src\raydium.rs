use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    system_program,
    sysvar::{clock, rent},
};
use spl_token::ID as TOKEN_PROGRAM_ID;
use spl_associated_token_account::ID as ASSOCIATED_TOKEN_PROGRAM_ID;
use std::str::FromStr;
use anyhow::Result;

// Raydium AMM Program ID
pub const RAYDIUM_AMM_PROGRAM_ID: &str = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";

// WSOL Token Mint
pub const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";

// Raydium AMM Instruction discriminator
pub const SWAP_INSTRUCTION_DISCRIMINATOR: u8 = 9;

#[derive(Debug, Clone)]
pub struct SwapInstruction {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
}

impl SwapInstruction {
    pub fn new(amount_in: u64, minimum_amount_out: u64) -> Self {
        Self {
            amount_in,
            minimum_amount_out,
        }
    }

    pub fn serialize(&self) -> Vec<u8> {
        let mut data = vec![SWAP_INSTRUCTION_DISCRIMINATOR];
        data.extend_from_slice(&self.amount_in.to_le_bytes());
        data.extend_from_slice(&self.minimum_amount_out.to_le_bytes());
        data
    }
}

pub fn create_swap_instruction(
    payer: &Pubkey,
    pool_id: &Pubkey,
    pool_authority: &Pubkey,
    pool_open_orders: &Pubkey,
    pool_target_orders: &Pubkey,
    pool_base_token_vault: &Pubkey,
    pool_quote_token_vault: &Pubkey,
    pool_withdraw_queue: &Pubkey,
    pool_temp_lp_token_account: &Pubkey,
    serum_program_id: &Pubkey,
    serum_market: &Pubkey,
    serum_bids: &Pubkey,
    serum_asks: &Pubkey,
    serum_event_queue: &Pubkey,
    serum_coin_vault_account: &Pubkey,
    serum_pc_vault_account: &Pubkey,
    serum_vault_signer: &Pubkey,
    user_source_token_account: &Pubkey,
    user_destination_token_account: &Pubkey,
    user_source_owner: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<Instruction> {
    let program_id = Pubkey::from_str(RAYDIUM_AMM_PROGRAM_ID)?;
    
    let accounts = vec![
        AccountMeta::new(*pool_id, false),
        AccountMeta::new_readonly(*pool_authority, false),
        AccountMeta::new(*pool_open_orders, false),
        AccountMeta::new(*pool_target_orders, false),
        AccountMeta::new(*pool_base_token_vault, false),
        AccountMeta::new(*pool_quote_token_vault, false),
        AccountMeta::new_readonly(*serum_program_id, false),
        AccountMeta::new(*serum_market, false),
        AccountMeta::new(*serum_bids, false),
        AccountMeta::new(*serum_asks, false),
        AccountMeta::new(*serum_event_queue, false),
        AccountMeta::new(*serum_coin_vault_account, false),
        AccountMeta::new(*serum_pc_vault_account, false),
        AccountMeta::new_readonly(*serum_vault_signer, false),
        AccountMeta::new(*user_source_token_account, false),
        AccountMeta::new(*user_destination_token_account, false),
        AccountMeta::new_readonly(*user_source_owner, true),
        AccountMeta::new_readonly(TOKEN_PROGRAM_ID, false),
    ];

    let swap_instruction = SwapInstruction::new(amount_in, minimum_amount_out);
    
    Ok(Instruction {
        program_id,
        accounts,
        data: swap_instruction.serialize(),
    })
}

pub fn get_associated_token_address(wallet: &Pubkey, token_mint: &Pubkey) -> Pubkey {
    spl_associated_token_account::get_associated_token_address(wallet, token_mint)
}

pub fn create_associated_token_account_instruction(
    payer: &Pubkey,
    wallet: &Pubkey,
    token_mint: &Pubkey,
) -> Instruction {
    spl_associated_token_account::instruction::create_associated_token_account(
        payer,
        wallet,
        token_mint,
        &TOKEN_PROGRAM_ID,
    )
}

// 计算池权限地址
pub fn find_pool_authority(pool_id: &Pubkey) -> (Pubkey, u8) {
    let program_id = Pubkey::from_str(RAYDIUM_AMM_PROGRAM_ID).unwrap();
    Pubkey::find_program_address(&[&pool_id.to_bytes()[..32]], &program_id)
}

// 计算Serum Market权限地址
pub fn find_serum_vault_signer(market: &Pubkey, serum_program_id: &Pubkey) -> (Pubkey, u64) {
    let seeds = [market.as_ref()];
    let mut nonce = 0u64;
    
    loop {
        let nonce_bytes = nonce.to_le_bytes();
        let seeds_with_nonce = [market.as_ref(), &nonce_bytes];
        
        match Pubkey::create_program_address(&seeds_with_nonce, serum_program_id) {
            Ok(address) => return (address, nonce),
            Err(_) => nonce += 1,
        }
    }
}